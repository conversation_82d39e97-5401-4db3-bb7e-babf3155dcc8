<?php

namespace App\Controller;

use App\Repository\DocumentRepository;
use App\Service\WorkflowAnalyticsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/analytics')]
class AnalyticsController extends AbstractController
{
    public function __construct(
        private DocumentRepository $documentRepository,
        private WorkflowAnalyticsService $analyticsService,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/', name: 'app_analytics_dashboard', methods: ['GET'])]
    public function dashboard(): Response
    {
        // Récupérer les KPIs principaux
        $kpis = $this->analyticsService->getMainKPIs();

        return $this->render('analytics/dashboard.html.twig', [
            'kpis' => $kpis,
        ]);
    }

    #[Route('/workflow-performance', name: 'app_analytics_workflow_performance', methods: ['GET'])]
    public function workflowPerformance(Request $request): JsonResponse
    {
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');
        $docType = $request->query->get('doc_type');

        try {
            $performance = $this->analyticsService->getWorkflowPerformance($startDate, $endDate, $docType);

            // Debug: log les données pour voir ce qui se passe
            error_log('Performance data: ' . json_encode($performance));

            return new JsonResponse($performance);
        } catch (\Exception $e) {
            error_log('Error in workflow performance: ' . $e->getMessage());
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    #[Route('/bottlenecks', name: 'app_analytics_bottlenecks', methods: ['GET'])]
    public function bottlenecks(Request $request): JsonResponse
    {
        $period = $request->query->get('period', '30'); // 30 jours par défaut

        $bottlenecks = $this->analyticsService->getBottlenecks((int)$period);

        return new JsonResponse($bottlenecks);
    }

    #[Route('/state-durations', name: 'app_analytics_state_durations', methods: ['GET'])]
    public function stateDurations(Request $request): JsonResponse
    {
        $state = $request->query->get('state');
        $period = $request->query->get('period', '90');

        $durations = $this->analyticsService->getStateDurations($state, (int)$period);

        return new JsonResponse($durations);
    }

    #[Route('/risky-documents', name: 'app_analytics_risky_documents', methods: ['GET'])]
    public function riskyDocuments(Request $request): JsonResponse
    {
        $threshold = $request->query->get('threshold', '30'); // 30 jours par défaut

        $riskyDocs = $this->analyticsService->getRiskyDocuments((int)$threshold);

        return new JsonResponse($riskyDocs);
    }

    #[Route('/workflow-trends', name: 'app_analytics_workflow_trends', methods: ['GET'])]
    public function workflowTrends(Request $request): JsonResponse
    {
        $period = $request->query->get('period', '6'); // 6 mois par défaut
        $granularity = $request->query->get('granularity', 'month'); // month, week, day

        $trends = $this->analyticsService->getWorkflowTrends((int)$period, $granularity);

        return new JsonResponse($trends);
    }

    #[Route('/document-flow', name: 'app_analytics_document_flow', methods: ['GET'])]
    public function documentFlow(Request $request): JsonResponse
    {
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');

        $flow = $this->analyticsService->getDocumentFlow($startDate, $endDate);

        return new JsonResponse($flow);
    }

    #[Route('/performance-by-user', name: 'app_analytics_performance_by_user', methods: ['GET'])]
    public function performanceByUser(Request $request): JsonResponse
    {
        $period = $request->query->get('period', '30');
        $state = $request->query->get('state');

        $performance = $this->analyticsService->getPerformanceByUser((int)$period, $state);

        return new JsonResponse($performance);
    }

    #[Route('/export-analytics', name: 'app_analytics_export', methods: ['GET'])]
    public function exportAnalytics(Request $request): Response
    {
        $format = $request->query->get('format', 'csv'); // csv, excel
        $type = $request->query->get('type', 'workflow_performance');
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');

        $data = $this->analyticsService->getExportData($type, $startDate, $endDate);

        if ($format === 'csv') {
            return $this->generateCsvResponse($data, $type);
        }

        // Pour Excel, on pourrait utiliser PhpSpreadsheet
        return $this->generateCsvResponse($data, $type);
    }

    #[Route('/state-analysis/{state}', name: 'app_analytics_state_analysis', methods: ['GET'])]
    public function stateAnalysis(string $state, Request $request): Response
    {
        $period = $request->query->get('period', '90');

        $analysis = $this->analyticsService->getStateDurations($state, (int)$period);

        return $this->render('analytics/state_analysis.html.twig', [
            'state' => $state,
            'analysis' => $analysis,
            'period' => $period,
        ]);
    }

    #[Route('/debug-data', name: 'app_analytics_debug', methods: ['GET'])]
    public function debugData(): JsonResponse
    {
        // Méthode de debug pour voir les données brutes
        try {
            $sql = "
                SELECT
                    d.id,
                    d.reference,
                    d.state_timestamps,
                    d.current_steps
                FROM document d
                WHERE d.state_timestamps IS NOT NULL
                AND d.state_timestamps != '{}'
                AND d.state_timestamps != ''
                LIMIT 5
            ";

            $results = $this->entityManager->getConnection()->executeQuery($sql)->fetchAllAssociative();

            return new JsonResponse([
                'sample_documents' => $results,
                'workflow_order' => [
                    'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
                    'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
                    'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
                    'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
                    'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
                    'Tirage_Plans'
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    #[Route('/workflow-simulator', name: 'app_analytics_workflow_simulator', methods: ['GET'])]
    public function workflowSimulator(): Response
    {
        // Interface pour simuler des changements de workflow et voir l'impact
        return $this->render('analytics/workflow_simulator.html.twig');
    }

    private function generateCsvResponse(array $data, string $type): Response
    {
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="analytics_' . $type . '_' . date('Y-m-d') . '.csv"');

        $csvContent = $this->analyticsService->formatDataAsCsv($data, $type);
        $response->setContent($csvContent);

        return $response;
    }
}
