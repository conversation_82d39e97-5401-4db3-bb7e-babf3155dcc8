<?php

/**
 * Script de test pour vérifier que la correction de la migration fonctionne
 */

require_once __DIR__ . '/vendor/autoload.php';

use Symfony\Component\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;

echo "=== Test de la correction de migration ===\n\n";

try {
    // Créer l'application console
    $application = new Application();
    
    // Charger les commandes depuis le kernel
    $kernel = new \App\Kernel('dev', true);
    $kernel->boot();
    
    $container = $kernel->getContainer();
    
    // Récupérer la commande de migration
    $command = $container->get('App\Command\MigrateLegacyDataOptimizedCommand');
    $application->add($command);
    
    // Préparer les arguments pour tester avec une limite
    $input = new ArrayInput([
        'command' => 'app:migrate-legacy-optimized',
        '--limit' => '5'  // Tester avec seulement 5 documents
    ]);
    
    $output = new BufferedOutput();
    
    echo "Lancement de la migration avec limite de 5 documents...\n";
    
    // Exécuter la commande
    $exitCode = $application->doRun($input, $output);
    
    // Afficher les résultats
    $outputContent = $output->fetch();
    echo "Sortie de la commande:\n";
    echo $outputContent . "\n";
    
    if ($exitCode === 0) {
        echo "✅ SUCCESS: La migration s'est exécutée sans erreur!\n";
        
        // Vérifier si des documents ont été traités
        if (strpos($outputContent, 'documents migrés avec succès') !== false) {
            echo "✅ Des documents ont été migrés avec succès.\n";
        }
        
        if (strpos($outputContent, 'visas créés avec succès') !== false) {
            echo "✅ Des visas ont été créés avec succès.\n";
        }
        
        if (strpos($outputContent, 'state_timestamps') !== false) {
            echo "✅ Les state_timestamps ont été traités.\n";
        }
        
    } else {
        echo "❌ FAILED: La migration a échoué avec le code de sortie: $exitCode\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Fin du test ===\n";
