{% extends 'base.html.twig' %}

{% block title %}Analyse de l'état {{ state }}{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .state-analysis {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: none;
        transition: box-shadow 0.3s ease;
    }

    .stat-card:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #009BFF;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .document-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 500;
    }

    .status-normal { background-color: #d4edda; color: #155724; }
    .status-attention { background-color: #fff3cd; color: #856404; }
    .status-warning { background-color: #f8d7da; color: #721c24; }
    .status-critical { background-color: #dc3545; color: white; }
    .status-completed { background-color: #e2e3e5; color: #383d41; }

    .chart-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
    }

    .filter-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .btn-analytics {
        background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }

    .btn-analytics:hover {
        background: linear-gradient(135deg, #0056b3 0%, #003d82 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 155, 255, 0.3);
    }

    .duration-bar {
        height: 20px;
        background: linear-gradient(90deg, #28a745 0%, #ffc107 50%, #dc3545 100%);
        border-radius: 10px;
        position: relative;
        overflow: hidden;
    }

    .duration-indicator {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block body %}
<div class="state-analysis">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h2 mb-2">
                            <i class="fas fa-microscope text-primary me-2"></i>
                            Analyse détaillée : {{ state }}
                        </h1>
                        <p class="text-muted">Analyse approfondie des durées et performances pour l'état {{ state }}</p>
                    </div>
                    <div>
                        <a href="{{ path('app_analytics_dashboard') }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Retour au dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-4">
                    <label for="periodFilter" class="form-label">Période d'analyse</label>
                    <select id="periodFilter" class="form-select">
                        <option value="30" {{ period == '30' ? 'selected' : '' }}>30 derniers jours</option>
                        <option value="60" {{ period == '60' ? 'selected' : '' }}>60 derniers jours</option>
                        <option value="90" {{ period == '90' ? 'selected' : '' }}>90 derniers jours</option>
                        <option value="180" {{ period == '180' ? 'selected' : '' }}>6 derniers mois</option>
                        <option value="365" {{ period == '365' ? 'selected' : '' }}>12 derniers mois</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="active">Documents actifs</option>
                        <option value="completed">Documents terminés</option>
                        <option value="critical">Critiques (>30j)</option>
                        <option value="warning">Attention (>15j)</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button id="applyFilters" class="btn btn-analytics w-100">
                        <i class="fas fa-filter me-2"></i>Appliquer les filtres
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        {% if analysis.statistics is defined %}
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="stat-value">{{ analysis.statistics.total_documents }}</div>
                        <div class="stat-label">Total Documents</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="stat-value">{{ analysis.statistics.active_documents }}</div>
                        <div class="stat-label">Actifs</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="stat-value">{{ analysis.statistics.completed_documents }}</div>
                        <div class="stat-label">Terminés</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="stat-value">{{ analysis.statistics.avg_duration }}</div>
                        <div class="stat-label">Durée Moy. (j)</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="stat-value">{{ analysis.statistics.avg_active_duration }}</div>
                        <div class="stat-label">Moy. Actifs (j)</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="stat-value">{{ analysis.statistics.avg_completed_duration }}</div>
                        <div class="stat-label">Moy. Terminés (j)</div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Graphiques -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar me-2"></i>
                        Distribution des durées
                    </h5>
                    <canvas id="durationDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie me-2"></i>
                        Répartition par statut
                    </h5>
                    <canvas id="statusDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Table des documents -->
        <div class="document-table">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    Documents dans l'état {{ state }}
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="documentsTable">
                        <thead class="table-light">
                            <tr>
                                <th>Référence</th>
                                <th>Révision</th>
                                <th>Type Doc</th>
                                <th>Type Proc</th>
                                <th>Date d'entrée</th>
                                <th>Date de sortie</th>
                                <th>Durée (jours)</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if analysis.documents is defined %}
                                {% for document in analysis.documents %}
                                <tr>
                                    <td>{{ document.reference }}</td>
                                    <td>{{ document.ref_rev }}</td>
                                    <td>{{ document.doc_type }}</td>
                                    <td>{{ document.proc_type }}</td>
                                    <td>{{ document.enter_date|date('d/m/Y H:i') }}</td>
                                    <td>
                                        {% if document.exit_date %}
                                            {{ document.exit_date|date('d/m/Y H:i') }}
                                        {% else %}
                                            <span class="text-muted">En cours</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">{{ document.duration_days }}</span>
                                            <div class="duration-bar flex-grow-1" style="width: 100px;">
                                                <div class="duration-indicator" style="width: {{ (document.duration_days / 60 * 100)|round }}%;"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge status-badge status-{{ document.status }}">
                                            {{ document.status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ path('app_time_tracking_document', {'id': document.document_id}) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        Aucun document trouvé pour cette période
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialiser les graphiques
    initCharts();
    
    // Gestionnaire pour les filtres
    $('#applyFilters').on('click', function() {
        const period = $('#periodFilter').val();
        const status = $('#statusFilter').val();
        
        // Recharger la page avec les nouveaux filtres
        const url = new URL(window.location);
        url.searchParams.set('period', period);
        if (status) {
            url.searchParams.set('status', status);
        } else {
            url.searchParams.delete('status');
        }
        window.location.href = url.toString();
    });
    
    // Initialiser DataTable pour la pagination et le tri
    $('#documentsTable').DataTable({
        pageLength: 25,
        order: [[6, 'desc']], // Trier par durée décroissante
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
        }
    });
});

function initCharts() {
    {% if analysis.documents is defined %}
    const documents = {{ analysis.documents|json_encode|raw }};
    
    // Graphique de distribution des durées
    const durationCtx = document.getElementById('durationDistributionChart').getContext('2d');
    const durations = documents.map(doc => doc.duration_days);
    
    // Créer des bins pour l'histogramme
    const bins = [0, 5, 10, 15, 30, 60, 120];
    const binCounts = new Array(bins.length - 1).fill(0);
    
    durations.forEach(duration => {
        for (let i = 0; i < bins.length - 1; i++) {
            if (duration >= bins[i] && duration < bins[i + 1]) {
                binCounts[i]++;
                break;
            }
        }
    });
    
    new Chart(durationCtx, {
        type: 'bar',
        data: {
            labels: ['0-5j', '5-10j', '10-15j', '15-30j', '30-60j', '60j+'],
            datasets: [{
                label: 'Nombre de documents',
                data: binCounts,
                backgroundColor: 'rgba(0, 155, 255, 0.7)',
                borderColor: 'rgba(0, 155, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Graphique de répartition par statut
    const statusCtx = document.getElementById('statusDistributionChart').getContext('2d');
    const statusCounts = {};
    documents.forEach(doc => {
        statusCounts[doc.status] = (statusCounts[doc.status] || 0) + 1;
    });
    
    const statusColors = {
        'normal': '#28a745',
        'attention': '#ffc107',
        'warning': '#fd7e14',
        'critical': '#dc3545',
        'completed': '#6c757d'
    };
    
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(statusCounts).map(s => s.charAt(0).toUpperCase() + s.slice(1)),
            datasets: [{
                data: Object.values(statusCounts),
                backgroundColor: Object.keys(statusCounts).map(status => statusColors[status] || '#6c757d')
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    {% endif %}
}
</script>
{% endblock %}
