<?php

/**
 * Script de validation post-migration pour vérifier les améliorations des state_timestamps
 * 
 * Ce script vérifie que la migration améliorée a correctement:
 * - Calculé les from_state
 * - Généré les dates d'exit appropriées
 * - Respecté l'ordre chronologique du workflow
 * - Maintenu la cohérence des données
 */

require_once __DIR__ . '/vendor/autoload.php';

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;

class MigrationValidator
{
    private Connection $connection;
    private array $validationResults = [];
    
    public function __construct()
    {
        // Configuration de la base de données
        $connectionParams = [
            'dbname' => 'your_database_name',
            'user' => 'your_username', 
            'password' => 'your_password',
            'host' => 'localhost',
            'driver' => 'pdo_mysql',
        ];
        
        $this->connection = DriverManager::getConnection($connectionParams);
    }
    
    /**
     * Exécuter toutes les validations
     */
    public function validateAll(): void
    {
        echo "=== Validation des Améliorations de Migration ===\n\n";
        
        $this->validateDataCompleteness();
        $this->validateFromStateAccuracy();
        $this->validateChronologicalOrder();
        $this->validateWorkflowLogic();
        $this->validateDataConsistency();
        $this->generateValidationReport();
    }
    
    /**
     * Valider la complétude des données
     */
    private function validateDataCompleteness(): void
    {
        echo "1. Validation de la complétude des données\n";
        echo str_repeat("-", 40) . "\n";
        
        // Vérifier que tous les documents ont des state_timestamps
        $totalDocs = $this->connection->fetchOne('SELECT COUNT(*) FROM document');
        $docsWithTimestamps = $this->connection->fetchOne(
            'SELECT COUNT(*) FROM document WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"'
        );
        
        $completenessRate = $totalDocs > 0 ? ($docsWithTimestamps / $totalDocs) * 100 : 0;
        
        echo "Documents avec state_timestamps: {$docsWithTimestamps}/{$totalDocs} ({$completenessRate}%)\n";
        
        // Vérifier les champs obligatoires
        $documentsWithIssues = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"'
        );
        
        $missingEnterDates = 0;
        $missingFromStates = 0;
        $totalEntries = 0;
        
        foreach ($documentsWithIssues as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) continue;
            
            foreach ($stateTimestamps as $state => $entries) {
                if (!is_array($entries)) continue;
                
                foreach ($entries as $entry) {
                    $totalEntries++;
                    
                    if (empty($entry['enter'])) {
                        $missingEnterDates++;
                    }
                    
                    if ($state !== 'BE_0' && empty($entry['from_state'])) {
                        $missingFromStates++;
                    }
                }
            }
        }
        
        echo "Entrées manquant de date d'entrée: {$missingEnterDates}/{$totalEntries}\n";
        echo "Entrées manquant de from_state: {$missingFromStates}/{$totalEntries}\n";
        
        $this->validationResults['completeness'] = [
            'total_docs' => $totalDocs,
            'docs_with_timestamps' => $docsWithTimestamps,
            'completeness_rate' => $completenessRate,
            'missing_enter_dates' => $missingEnterDates,
            'missing_from_states' => $missingFromStates,
            'total_entries' => $totalEntries
        ];
        
        echo $completenessRate >= 95 ? "✅ PASSED\n\n" : "❌ FAILED\n\n";
    }
    
    /**
     * Valider la précision des from_state
     */
    private function validateFromStateAccuracy(): void
    {
        echo "2. Validation de la précision des from_state\n";
        echo str_repeat("-", 40) . "\n";
        
        $workflowOrder = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];
        
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"
             LIMIT 100' // Échantillon pour validation
        );
        
        $correctFromStates = 0;
        $incorrectFromStates = 0;
        $totalTransitions = 0;
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) continue;
            
            $stateSequence = $this->extractStateSequence($stateTimestamps);
            
            for ($i = 1; $i < count($stateSequence); $i++) {
                $totalTransitions++;
                $currentEntry = $stateSequence[$i];
                $previousEntry = $stateSequence[$i - 1];
                
                $expectedFromState = $previousEntry['state'];
                $actualFromState = $currentEntry['from_state'];
                
                if ($actualFromState === $expectedFromState) {
                    $correctFromStates++;
                } else {
                    $incorrectFromStates++;
                    
                    // Vérifier si c'est une transition valide selon le workflow
                    if ($this->isValidWorkflowTransition($expectedFromState, $currentEntry['state'], $workflowOrder)) {
                        // Transition valide mais from_state incorrect
                        echo "⚠️  Document {$doc['reference']}: from_state incorrect pour {$currentEntry['state']} (attendu: {$expectedFromState}, obtenu: {$actualFromState})\n";
                    }
                }
            }
        }
        
        $accuracy = $totalTransitions > 0 ? ($correctFromStates / $totalTransitions) * 100 : 0;
        
        echo "From_state corrects: {$correctFromStates}/{$totalTransitions} ({$accuracy}%)\n";
        
        $this->validationResults['from_state_accuracy'] = [
            'correct' => $correctFromStates,
            'incorrect' => $incorrectFromStates,
            'total' => $totalTransitions,
            'accuracy' => $accuracy
        ];
        
        echo $accuracy >= 90 ? "✅ PASSED\n\n" : "❌ FAILED\n\n";
    }
    
    /**
     * Valider l'ordre chronologique
     */
    private function validateChronologicalOrder(): void
    {
        echo "3. Validation de l'ordre chronologique\n";
        echo str_repeat("-", 40) . "\n";
        
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"
             LIMIT 100'
        );
        
        $chronologicallyValid = 0;
        $chronologicallyInvalid = 0;
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) continue;
            
            $stateSequence = $this->extractStateSequence($stateTimestamps);
            
            $isValid = true;
            for ($i = 1; $i < count($stateSequence); $i++) {
                $prevDate = $stateSequence[$i - 1]['enter'];
                $currentDate = $stateSequence[$i]['enter'];
                
                if ($prevDate && $currentDate && $prevDate > $currentDate) {
                    $isValid = false;
                    echo "⚠️  Document {$doc['reference']}: ordre chronologique invalide\n";
                    break;
                }
            }
            
            if ($isValid) {
                $chronologicallyValid++;
            } else {
                $chronologicallyInvalid++;
            }
        }
        
        $chronologyRate = ($chronologicallyValid + $chronologicallyInvalid) > 0 ? 
            ($chronologicallyValid / ($chronologicallyValid + $chronologicallyInvalid)) * 100 : 0;
        
        echo "Documents chronologiquement valides: {$chronologicallyValid}/{$chronologicallyValid + $chronologicallyInvalid} ({$chronologyRate}%)\n";
        
        $this->validationResults['chronological_order'] = [
            'valid' => $chronologicallyValid,
            'invalid' => $chronologicallyInvalid,
            'rate' => $chronologyRate
        ];
        
        echo $chronologyRate >= 95 ? "✅ PASSED\n\n" : "❌ FAILED\n\n";
    }
    
    /**
     * Valider la logique du workflow
     */
    private function validateWorkflowLogic(): void
    {
        echo "4. Validation de la logique du workflow\n";
        echo str_repeat("-", 40) . "\n";
        
        // Vérifier que les transitions respectent les règles métier
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps, doc_type, proc_type FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"
             LIMIT 50'
        );
        
        $validWorkflows = 0;
        $invalidWorkflows = 0;
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) continue;
            
            $stateSequence = $this->extractStateSequence($stateTimestamps);
            
            if ($this->validateDocumentWorkflow($stateSequence, $doc['doc_type'], $doc['proc_type'])) {
                $validWorkflows++;
            } else {
                $invalidWorkflows++;
                echo "⚠️  Document {$doc['reference']}: workflow invalide pour type {$doc['doc_type']}\n";
            }
        }
        
        $workflowRate = ($validWorkflows + $invalidWorkflows) > 0 ? 
            ($validWorkflows / ($validWorkflows + $invalidWorkflows)) * 100 : 0;
        
        echo "Workflows valides: {$validWorkflows}/{$validWorkflows + $invalidWorkflows} ({$workflowRate}%)\n";
        
        $this->validationResults['workflow_logic'] = [
            'valid' => $validWorkflows,
            'invalid' => $invalidWorkflows,
            'rate' => $workflowRate
        ];
        
        echo $workflowRate >= 85 ? "✅ PASSED\n\n" : "❌ FAILED\n\n";
    }
    
    /**
     * Valider la cohérence des données
     */
    private function validateDataConsistency(): void
    {
        echo "5. Validation de la cohérence des données\n";
        echo str_repeat("-", 40) . "\n";
        
        // Vérifier la cohérence entre state_timestamps et current_steps
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps, current_steps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"
             AND current_steps IS NOT NULL AND current_steps != "{}"
             LIMIT 100'
        );
        
        $consistentDocs = 0;
        $inconsistentDocs = 0;
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            $currentSteps = json_decode($doc['current_steps'], true);
            
            if (!is_array($stateTimestamps) || !is_array($currentSteps)) continue;
            
            $activeStatesFromTimestamps = $this->getActiveStatesFromTimestamps($stateTimestamps);
            $activeStatesFromCurrentSteps = array_keys(array_filter($currentSteps));
            
            if ($this->arraysEqual($activeStatesFromTimestamps, $activeStatesFromCurrentSteps)) {
                $consistentDocs++;
            } else {
                $inconsistentDocs++;
                echo "⚠️  Document {$doc['reference']}: incohérence entre state_timestamps et current_steps\n";
            }
        }
        
        $consistencyRate = ($consistentDocs + $inconsistentDocs) > 0 ? 
            ($consistentDocs / ($consistentDocs + $inconsistentDocs)) * 100 : 0;
        
        echo "Documents cohérents: {$consistentDocs}/{$consistentDocs + $inconsistentDocs} ({$consistencyRate}%)\n";
        
        $this->validationResults['data_consistency'] = [
            'consistent' => $consistentDocs,
            'inconsistent' => $inconsistentDocs,
            'rate' => $consistencyRate
        ];
        
        echo $consistencyRate >= 90 ? "✅ PASSED\n\n" : "❌ FAILED\n\n";
    }
    
    /**
     * Générer le rapport de validation
     */
    private function generateValidationReport(): void
    {
        echo "=== RAPPORT DE VALIDATION ===\n\n";
        
        $overallScore = 0;
        $totalTests = 0;
        
        foreach ($this->validationResults as $testName => $results) {
            $score = 0;
            switch ($testName) {
                case 'completeness':
                    $score = $results['completeness_rate'];
                    break;
                case 'from_state_accuracy':
                    $score = $results['accuracy'];
                    break;
                case 'chronological_order':
                    $score = $results['rate'];
                    break;
                case 'workflow_logic':
                    $score = $results['rate'];
                    break;
                case 'data_consistency':
                    $score = $results['rate'];
                    break;
            }
            
            $overallScore += $score;
            $totalTests++;
        }
        
        $averageScore = $totalTests > 0 ? $overallScore / $totalTests : 0;
        
        echo "Score global: " . round($averageScore, 2) . "%\n\n";
        
        if ($averageScore >= 90) {
            echo "🎉 EXCELLENT: La migration améliorée fonctionne parfaitement!\n";
        } elseif ($averageScore >= 75) {
            echo "✅ BON: La migration améliorée fonctionne bien avec quelques ajustements mineurs.\n";
        } elseif ($averageScore >= 60) {
            echo "⚠️  MOYEN: La migration améliorée nécessite des corrections.\n";
        } else {
            echo "❌ INSUFFISANT: La migration améliorée nécessite des corrections majeures.\n";
        }
        
        echo "\nActions recommandées:\n";
        if ($averageScore < 90) {
            echo "- Analyser les cas d'échec spécifiques\n";
            echo "- Ajuster la logique de migration\n";
            echo "- Relancer la migration sur les documents problématiques\n";
        }
        echo "- Mettre en place un monitoring continu\n";
        echo "- Documenter les améliorations pour l'équipe\n";
    }
    
    // Méthodes utilitaires
    
    private function extractStateSequence(array $stateTimestamps): array
    {
        $sequence = [];
        
        foreach ($stateTimestamps as $state => $entries) {
            if (!is_array($entries)) continue;
            
            foreach ($entries as $entry) {
                $sequence[] = [
                    'state' => $state,
                    'enter' => $entry['enter'] ?? null,
                    'exit' => $entry['exit'] ?? null,
                    'from_state' => $entry['from_state'] ?? null
                ];
            }
        }
        
        usort($sequence, function($a, $b) {
            return strcmp($a['enter'] ?? '', $b['enter'] ?? '');
        });
        
        return $sequence;
    }
    
    private function isValidWorkflowTransition(string $fromState, string $toState, array $workflowOrder): bool
    {
        $fromIndex = array_search($fromState, $workflowOrder);
        $toIndex = array_search($toState, $workflowOrder);
        
        if ($fromIndex === false || $toIndex === false) {
            return true; // États non dans l'ordre standard, considérer comme valide
        }
        
        return $toIndex >= $fromIndex; // Transition vers l'avant ou parallèle
    }
    
    private function validateDocumentWorkflow(array $stateSequence, ?string $docType, ?string $procType): bool
    {
        // Validation simplifiée - en réalité, cela dépendrait des règles métier complexes
        
        // Vérifier que tous les documents commencent par BE_0
        if (empty($stateSequence) || $stateSequence[0]['state'] !== 'BE_0') {
            return false;
        }
        
        // Vérifier les transitions BE obligatoires
        $hasValidBESequence = false;
        foreach ($stateSequence as $entry) {
            if (in_array($entry['state'], ['BE_0', 'BE_1', 'BE'])) {
                $hasValidBESequence = true;
                break;
            }
        }
        
        return $hasValidBESequence;
    }
    
    private function getActiveStatesFromTimestamps(array $stateTimestamps): array
    {
        $activeStates = [];
        
        foreach ($stateTimestamps as $state => $entries) {
            if (!is_array($entries)) continue;
            
            foreach ($entries as $entry) {
                if (($entry['exit'] ?? null) === null) {
                    $activeStates[] = $state;
                }
            }
        }
        
        return $activeStates;
    }
    
    private function arraysEqual(array $a, array $b): bool
    {
        sort($a);
        sort($b);
        return $a === $b;
    }
}

// Exécuter la validation
try {
    $validator = new MigrationValidator();
    $validator->validateAll();
} catch (Exception $e) {
    echo "Erreur lors de la validation: " . $e->getMessage() . "\n";
    echo "Veuillez vérifier la configuration de la base de données.\n";
}
