<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;

class WorkflowAnalyticsService
{
    private const WORKFLOW_ORDER = [
        'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
        'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
        'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
        'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
        'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
        'Tirage_Plans'
    ];

    public function __construct(
        private DocumentRepository $documentRepository,
        private EntityManagerInterface $entityManager,
        private Connection $connection
    ) {}

    /**
     * Récupère les KPIs principaux du système
     */
    public function getMainKPIs(): array
    {
        $sql = "
            SELECT
                COUNT(DISTINCT d.id) as total_documents,
                COUNT(DISTINCT CASE WHEN d.state_timestamps IS NOT NULL AND d.state_timestamps != '{}' THEN d.id END) as documents_with_tracking,
                AVG(CASE
                    WHEN d.state_timestamps IS NOT NULL AND d.state_timestamps != '{}'
                    THEN DATEDIFF(NOW(), STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE[0].enter')), '%Y-%m-%d %H:%i:%s'))
                END) as avg_days_since_be,
                COUNT(DISTINCT CASE
                    WHEN d.current_steps IS NOT NULL AND d.current_steps != '{}'
                    THEN d.id
                END) as active_documents
            FROM document d
        ";

        $result = $this->connection->executeQuery($sql)->fetchAssociative();

        // Calculer les documents à risque (plus de 30 jours dans le même état)
        $riskyDocuments = $this->getRiskyDocuments(30);

        return [
            'total_documents' => (int)$result['total_documents'],
            'documents_with_tracking' => (int)$result['documents_with_tracking'],
            'active_documents' => (int)$result['active_documents'],
            'avg_days_since_be' => round((float)$result['avg_days_since_be'], 1),
            'risky_documents_count' => count($riskyDocuments),
            'tracking_coverage' => round(
                ($result['documents_with_tracking'] / max($result['total_documents'], 1)) * 100,
                1
            )
        ];
    }

    /**
     * Analyse les performances du workflow par état
     */
    public function getWorkflowPerformance(?string $startDate = null, ?string $endDate = null, ?string $docType = null): array
    {
        $whereConditions = ["d.state_timestamps IS NOT NULL", "d.state_timestamps != '{}'"];
        $parameters = [];

        if ($startDate) {
            $whereConditions[] = "d.id >= (SELECT MIN(id) FROM document WHERE id >= ?)";
            $parameters[] = $startDate;
        }

        if ($endDate) {
            $whereConditions[] = "d.id <= (SELECT MAX(id) FROM document WHERE id <= ?)";
            $parameters[] = $endDate;
        }

        if ($docType) {
            $whereConditions[] = "d.doc_type = ?";
            $parameters[] = $docType;
        }

        $performance = [];

        foreach (self::WORKFLOW_ORDER as $state) {
            $sql = "
                SELECT
                    COUNT(*) as document_count,
                    AVG(
                        CASE
                            WHEN jt.exit_date IS NOT NULL AND jt.exit_date != ''
                            THEN DATEDIFF(
                                STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                                STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                            )
                            WHEN jt.exit_date IS NULL OR jt.exit_date = ''
                            THEN DATEDIFF(
                                NOW(),
                                STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                            )
                        END
                    ) as avg_duration_days,
                    MIN(
                        CASE
                            WHEN jt.exit_date IS NOT NULL AND jt.exit_date != ''
                            THEN DATEDIFF(
                                STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                                STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                            )
                            ELSE DATEDIFF(
                                NOW(),
                                STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                            )
                        END
                    ) as min_duration_days,
                    MAX(
                        CASE
                            WHEN jt.exit_date IS NOT NULL AND jt.exit_date != ''
                            THEN DATEDIFF(
                                STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                                STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                            )
                            ELSE DATEDIFF(
                                NOW(),
                                STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                            )
                        END
                    ) as max_duration_days
                FROM document d
                CROSS JOIN JSON_TABLE(
                    d.state_timestamps,
                    '$.\"{$state}\"[*]' COLUMNS (
                        enter_date VARCHAR(255) PATH '$.enter',
                        exit_date VARCHAR(255) PATH '$.exit'
                    )
                ) jt
                WHERE " . implode(' AND ', $whereConditions) . "
                AND jt.enter_date IS NOT NULL
                AND jt.enter_date != ''
                AND STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') IS NOT NULL
                AND STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
            ";

            $result = $this->connection->executeQuery($sql, $parameters)->fetchAssociative();

            if ($result && $result['document_count'] > 0) {
                $performance[$state] = [
                    'state' => $state,
                    'document_count' => (int)$result['document_count'],
                    'avg_duration_days' => round((float)$result['avg_duration_days'], 1),
                    'min_duration_days' => max(0, (int)$result['min_duration_days']), // Éviter les valeurs négatives
                    'max_duration_days' => min(365, (int)$result['max_duration_days']), // Limiter à 1 an max
                ];
            }
        }

        return array_filter($performance, fn($p) => $p['document_count'] > 0);
    }

    /**
     * Identifie les goulots d'étranglement
     */
    public function getBottlenecks(int $periodDays = 30): array
    {
        $performance = $this->getWorkflowPerformance();

        // Trier par durée moyenne décroissante
        usort($performance, fn($a, $b) => $b['avg_duration_days'] <=> $a['avg_duration_days']);

        // Calculer les métriques de goulot d'étranglement
        $bottlenecks = [];
        foreach ($performance as $index => $state) {
            if ($state['document_count'] >= 5) { // Minimum 5 documents pour être significatif
                $bottlenecks[] = [
                    'state' => $state['state'],
                    'avg_duration_days' => $state['avg_duration_days'],
                    'document_count' => $state['document_count'],
                    'severity_rank' => $index + 1,
                    'severity_score' => $this->calculateSeverityScore($state),
                ];
            }
        }

        return array_slice($bottlenecks, 0, 10); // Top 10 des goulots
    }

    /**
     * Calcule un score de sévérité pour un goulot d'étranglement
     */
    private function calculateSeverityScore(array $stateData): float
    {
        // Score basé sur la durée moyenne et le nombre de documents
        $durationWeight = min($stateData['avg_duration_days'] / 30, 5); // Max 5 points pour la durée
        $volumeWeight = min($stateData['document_count'] / 50, 3); // Max 3 points pour le volume

        return round($durationWeight + $volumeWeight, 2);
    }

    /**
     * Récupère les documents à risque
     */
    public function getRiskyDocuments(int $thresholdDays = 30): array
    {
        $sql = "
            SELECT
                d.id,
                d.reference,
                d.ref_rev,
                d.ref_title_fra,
                d.doc_type,
                d.current_steps,
                DATEDIFF(NOW(), STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')) as days_in_current_state,
                jt.state_name
            FROM document d
            CROSS JOIN JSON_TABLE(
                d.state_timestamps,
                '$.*[*]' COLUMNS (
                    state_name VARCHAR(255) PATH '$',
                    enter_date VARCHAR(255) PATH '$.enter',
                    exit_date VARCHAR(255) PATH '$.exit'
                )
            ) jt
            WHERE d.current_steps IS NOT NULL
            AND d.current_steps != '{}'
            AND jt.exit_date IS NULL
            AND DATEDIFF(NOW(), STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')) > ?
            ORDER BY days_in_current_state DESC
            LIMIT 50
        ";

        $results = $this->connection->executeQuery($sql, [$thresholdDays])->fetchAllAssociative();

        return array_map(function($row) {
            return [
                'id' => (int)$row['id'],
                'reference' => $row['reference'],
                'ref_rev' => $row['ref_rev'],
                'title' => $row['ref_title_fra'],
                'doc_type' => $row['doc_type'],
                'current_steps' => json_decode($row['current_steps'], true),
                'days_in_current_state' => (int)$row['days_in_current_state'],
                'current_state' => $row['state_name'],
                'risk_level' => $this->calculateRiskLevel((int)$row['days_in_current_state'])
            ];
        }, $results);
    }

    /**
     * Calcule le niveau de risque basé sur les jours dans l'état actuel
     */
    private function calculateRiskLevel(int $days): string
    {
        if ($days > 60) return 'critical';
        if ($days > 30) return 'high';
        if ($days > 15) return 'medium';
        return 'low';
    }

    /**
     * Analyse les tendances du workflow sur une période
     */
    public function getWorkflowTrends(int $periodMonths = 6, string $granularity = 'month'): array
    {
        $dateFormat = match($granularity) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m'
        };

        $sql = "
            SELECT
                DATE_FORMAT(STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s'), ?) as period,
                COUNT(DISTINCT d.id) as document_count,
                AVG(
                    CASE
                        WHEN jt.exit_date IS NOT NULL
                        THEN DATEDIFF(
                            STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                            STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                        )
                    END
                ) as avg_processing_time
            FROM document d
            CROSS JOIN JSON_TABLE(
                d.state_timestamps,
                '$.*[*]' COLUMNS (
                    enter_date VARCHAR(255) PATH '$.enter',
                    exit_date VARCHAR(255) PATH '$.exit'
                )
            ) jt
            WHERE jt.enter_date IS NOT NULL
            AND STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY period
            ORDER BY period
        ";

        $results = $this->connection->executeQuery($sql, [$dateFormat, $periodMonths])->fetchAllAssociative();

        return array_map(function($row) {
            return [
                'period' => $row['period'],
                'document_count' => (int)$row['document_count'],
                'avg_processing_time' => round((float)$row['avg_processing_time'], 1)
            ];
        }, $results);
    }

    /**
     * Analyse le flux de documents entre les états
     */
    public function getDocumentFlow(?string $startDate = null, ?string $endDate = null): array
    {
        $whereConditions = ["d.state_timestamps IS NOT NULL", "d.state_timestamps != '{}'"];
        $parameters = [];

        if ($startDate) {
            $whereConditions[] = "STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') >= ?";
            $parameters[] = $startDate;
        }

        if ($endDate) {
            $whereConditions[] = "STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') <= ?";
            $parameters[] = $endDate;
        }

        $sql = "
            SELECT
                jt.from_state,
                jt.current_state,
                COUNT(*) as transition_count,
                AVG(
                    DATEDIFF(
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s'),
                        STR_TO_DATE(prev_jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                ) as avg_transition_time
            FROM document d
            CROSS JOIN JSON_TABLE(
                d.state_timestamps,
                '$.*[*]' COLUMNS (
                    current_state VARCHAR(255) PATH '$',
                    enter_date VARCHAR(255) PATH '$.enter',
                    from_state VARCHAR(255) PATH '$.from_state'
                )
            ) jt
            LEFT JOIN JSON_TABLE(
                d.state_timestamps,
                '$.*[*]' COLUMNS (
                    enter_date VARCHAR(255) PATH '$.enter'
                )
            ) prev_jt ON prev_jt.enter_date < jt.enter_date
            WHERE " . implode(' AND ', $whereConditions) . "
            AND jt.from_state IS NOT NULL
            GROUP BY jt.from_state, jt.current_state
            ORDER BY transition_count DESC
        ";

        $results = $this->connection->executeQuery($sql, $parameters)->fetchAllAssociative();

        return array_map(function($row) {
            return [
                'from_state' => $row['from_state'],
                'to_state' => $row['current_state'],
                'transition_count' => (int)$row['transition_count'],
                'avg_transition_time' => round((float)$row['avg_transition_time'], 1)
            ];
        }, $results);
    }

    /**
     * Analyse les performances par utilisateur
     */
    public function getPerformanceByUser(int $periodDays = 30, ?string $state = null): array
    {
        $whereConditions = [
            "v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
            "v.status = 'valid'"
        ];
        $parameters = [$periodDays];

        if ($state) {
            $whereConditions[] = "v.name = ?";
            $parameters[] = 'visa_' . $state;
        }

        $sql = "
            SELECT
                u.id as user_id,
                u.nom,
                u.prenom,
                COUNT(DISTINCT v.id) as visas_count,
                COUNT(DISTINCT d.id) as documents_processed,
                AVG(
                    DATEDIFF(
                        v.created_at,
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                ) as avg_processing_time,
                MIN(
                    DATEDIFF(
                        v.created_at,
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                ) as min_processing_time,
                MAX(
                    DATEDIFF(
                        v.created_at,
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                ) as max_processing_time
            FROM visa v
            JOIN user u ON v.validator_id = u.id
            JOIN document d ON v.released_drawing_id = d.id
            CROSS JOIN JSON_TABLE(
                d.state_timestamps,
                '$.*[*]' COLUMNS (
                    enter_date VARCHAR(255) PATH '$.enter',
                    exit_date VARCHAR(255) PATH '$.exit'
                )
            ) jt
            WHERE " . implode(' AND ', $whereConditions) . "
            AND jt.exit_date IS NOT NULL
            GROUP BY u.id, u.nom, u.prenom
            HAVING documents_processed >= 3
            ORDER BY avg_processing_time ASC
        ";

        $results = $this->connection->executeQuery($sql, $parameters)->fetchAllAssociative();

        return array_map(function($row) {
            return [
                'user_id' => (int)$row['user_id'],
                'user_name' => $row['prenom'] . ' ' . $row['nom'],
                'visas_count' => (int)$row['visas_count'],
                'documents_processed' => (int)$row['documents_processed'],
                'avg_processing_time' => round((float)$row['avg_processing_time'], 1),
                'min_processing_time' => (int)$row['min_processing_time'],
                'max_processing_time' => (int)$row['max_processing_time'],
                'efficiency_score' => $this->calculateEfficiencyScore($row)
            ];
        }, $results);
    }

    /**
     * Calcule un score d'efficacité pour un utilisateur
     */
    private function calculateEfficiencyScore(array $userData): float
    {
        $avgTime = (float)$userData['avg_processing_time'];
        $volume = (int)$userData['documents_processed'];

        // Score basé sur la rapidité (inversement proportionnel au temps) et le volume
        $timeScore = max(0, 10 - ($avgTime / 5)); // Max 10 points, diminue avec le temps
        $volumeScore = min($volume / 10, 5); // Max 5 points pour le volume

        return round($timeScore + $volumeScore, 2);
    }

    /**
     * Analyse détaillée des durées pour un état spécifique
     */
    public function getStateDurations(?string $state = null, int $periodDays = 90): array
    {
        if (!$state) {
            return [];
        }

        $sql = "
            SELECT
                d.id,
                d.reference,
                d.ref_rev,
                d.doc_type,
                d.proc_type,
                jt.enter_date,
                jt.exit_date,
                CASE
                    WHEN jt.exit_date IS NOT NULL
                    THEN DATEDIFF(
                        STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                    ELSE DATEDIFF(
                        NOW(),
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                END as duration_days,
                CASE WHEN jt.exit_date IS NULL THEN 1 ELSE 0 END as is_active
            FROM document d
            CROSS JOIN JSON_TABLE(
                d.state_timestamps,
                '$.\"{$state}\"[*]' COLUMNS (
                    enter_date VARCHAR(255) PATH '$.enter',
                    exit_date VARCHAR(255) PATH '$.exit'
                )
            ) jt
            WHERE jt.enter_date IS NOT NULL
            AND STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ORDER BY duration_days DESC
        ";

        $results = $this->connection->executeQuery($sql, [$periodDays])->fetchAllAssociative();

        $durations = array_map(function($row) {
            return [
                'document_id' => (int)$row['id'],
                'reference' => $row['reference'],
                'ref_rev' => $row['ref_rev'],
                'doc_type' => $row['doc_type'],
                'proc_type' => $row['proc_type'],
                'enter_date' => $row['enter_date'],
                'exit_date' => $row['exit_date'],
                'duration_days' => (int)$row['duration_days'],
                'is_active' => (bool)$row['is_active'],
                'status' => $this->getStatusFromDuration((int)$row['duration_days'], (bool)$row['is_active'])
            ];
        }, $results);

        // Calculer les statistiques
        $activeDurations = array_filter($durations, fn($d) => $d['is_active']);
        $completedDurations = array_filter($durations, fn($d) => !$d['is_active']);

        $stats = [
            'total_documents' => count($durations),
            'active_documents' => count($activeDurations),
            'completed_documents' => count($completedDurations),
            'avg_duration' => count($durations) > 0 ? round(array_sum(array_column($durations, 'duration_days')) / count($durations), 1) : 0,
            'avg_active_duration' => count($activeDurations) > 0 ? round(array_sum(array_column($activeDurations, 'duration_days')) / count($activeDurations), 1) : 0,
            'avg_completed_duration' => count($completedDurations) > 0 ? round(array_sum(array_column($completedDurations, 'duration_days')) / count($completedDurations), 1) : 0,
        ];

        return [
            'state' => $state,
            'statistics' => $stats,
            'documents' => $durations
        ];
    }

    /**
     * Détermine le statut basé sur la durée
     */
    private function getStatusFromDuration(int $days, bool $isActive): string
    {
        if (!$isActive) {
            return 'completed';
        }

        if ($days > 30) return 'critical';
        if ($days > 15) return 'warning';
        if ($days > 7) return 'attention';
        return 'normal';
    }

    /**
     * Récupère les données pour l'export
     */
    public function getExportData(string $type, ?string $startDate = null, ?string $endDate = null): array
    {
        switch ($type) {
            case 'workflow_performance':
                return $this->getWorkflowPerformance($startDate, $endDate);
            case 'bottlenecks':
                return $this->getBottlenecks();
            case 'risky_documents':
                return $this->getRiskyDocuments();
            default:
                return [];
        }
    }

    /**
     * Formate les données en CSV
     */
    public function formatDataAsCsv(array $data, string $type): string
    {
        if (empty($data)) {
            return '';
        }

        $csv = '';
        $headers = array_keys($data[0]);
        $csv .= implode(';', $headers) . "\n";

        foreach ($data as $row) {
            $csv .= implode(';', array_values($row)) . "\n";
        }

        return $csv;
    }
}
