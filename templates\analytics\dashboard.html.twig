{% extends 'base.html.twig' %}

{% block title %}Analyse des Workflows{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .analytics-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 0;
    }

    .dashboard-overlay {
        background: rgba(255, 255, 255, 0.95);
        min-height: 100vh;
        backdrop-filter: blur(10px);
    }

    .kpi-card {
        background: linear-gradient(135deg, #009BFF 0%, #00d4ff 100%);
        color: white;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 155, 255, 0.3);
        transition: all 0.4s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .kpi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }

    .kpi-card:hover::before {
        transform: translateX(100%);
    }

    .kpi-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 155, 255, 0.4);
    }

    .kpi-value {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 2;
    }

    .kpi-label {
        font-size: 0.9rem;
        opacity: 0.95;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 600;
        position: relative;
        z-index: 2;
    }

    .chart-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(15px);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .chart-header {
        background: linear-gradient(135deg, #009BFF 0%, #00d4ff 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 1.5rem;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .chart-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .chart-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 2;
    }

    .filter-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(15px);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .btn-analytics {
        background: linear-gradient(135deg, #009BFF 0%, #00d4ff 100%);
        border: none;
        border-radius: 15px;
        color: white;
        font-weight: 600;
        padding: 1rem 2rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 155, 255, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
    }

    .btn-analytics::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-analytics:hover::before {
        left: 100%;
    }

    .btn-analytics:hover {
        background: linear-gradient(135deg, #0056b3 0%, #009BFF 100%);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 155, 255, 0.4);
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 3rem;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
        border-width: 0.3rem;
    }

    .risk-badge {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .risk-low {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }
    .risk-medium {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
    }
    .risk-high {
        background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
        color: white;
    }
    .risk-critical {
        background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
        color: white;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3); }
        50% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.6); }
        100% { box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3); }
    }

    .metric-trend {
        font-size: 0.9rem;
        margin-top: 0.5rem;
        font-weight: 600;
    }

    .trend-up {
        color: #28a745;
        text-shadow: 0 1px 2px rgba(40, 167, 69, 0.3);
    }
    .trend-down {
        color: #dc3545;
        text-shadow: 0 1px 2px rgba(220, 53, 69, 0.3);
    }
    .trend-stable {
        color: #6c757d;
        text-shadow: 0 1px 2px rgba(108, 117, 125, 0.3);
    }

    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid rgba(0, 155, 255, 0.2);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #009BFF;
        box-shadow: 0 0 0 0.2rem rgba(0, 155, 255, 0.25);
        background: white;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
    }

    h1, h2, h3, h4, h5 {
        color: #2c3e50;
        font-weight: 700;
    }

    .text-muted {
        color: #6c757d !important;
        font-weight: 500;
    }

    .list-group-item {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 155, 255, 0.1);
        border-radius: 12px !important;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background: rgba(0, 155, 255, 0.05);
        transform: translateX(5px);
    }

    .chart-container {
        position: relative;
        height: 400px;
    }

    canvas {
        border-radius: 0 0 20px 20px;
    }
</style>
{% endblock %}

{% block body %}
<div class="analytics-dashboard">
    <div class="dashboard-overlay">
        <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h2 mb-3">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Analyse des Workflows et Performance
                </h1>
                <p class="text-muted">Tableau de bord d'analyse des durées, goulots d'étranglement et performance du système</p>
            </div>
        </div>

        <!-- KPIs -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.total_documents|number_format }}</div>
                        <div class="kpi-label">Total Documents</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.active_documents|number_format }}</div>
                        <div class="kpi-label">Documents Actifs</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.avg_days_since_be }}</div>
                        <div class="kpi-label">Jours Moy. depuis BE</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.risky_documents_count }}</div>
                        <div class="kpi-label">Documents à Risque</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.tracking_coverage }}%</div>
                        <div class="kpi-label">Couverture Suivi</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.documents_with_tracking|number_format }}</div>
                        <div class="kpi-label">Avec Timestamps</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label for="startDate" class="form-label">Date de début</label>
                    <input type="date" id="startDate" class="form-control" value="{{ 'now'|date_modify('-30 days')|date('Y-m-d') }}">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">Date de fin</label>
                    <input type="date" id="endDate" class="form-control" value="{{ 'now'|date('Y-m-d') }}">
                </div>
                <div class="col-md-3">
                    <label for="docTypeFilter" class="form-label">Type de document</label>
                    <select id="docTypeFilter" class="form-select">
                        <option value="">Tous les types</option>
                        <option value="ASSY">ASSY</option>
                        <option value="PUR">PUR</option>
                        <option value="DOC">DOC</option>
                        <option value="MACH">MACH</option>
                        <option value="MOLD">MOLD</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button id="applyFilters" class="btn btn-analytics w-100">
                        <i class="fas fa-filter me-2"></i>Appliquer les filtres
                    </button>
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="row">
            <!-- Performance par état -->
            <div class="col-lg-6 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-stopwatch me-2"></i>
                            Performance par État
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="performanceLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <canvas id="performanceChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Goulots d'étranglement -->
            <div class="col-lg-6 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Goulots d'Étranglement
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="bottlenecksLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <canvas id="bottlenecksChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Tendances temporelles -->
            <div class="col-lg-8 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-line me-2"></i>
                            Tendances des Temps de Traitement
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="trendsLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <canvas id="trendsChart" width="600" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Documents à risque -->
            <div class="col-lg-4 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Documents à Risque
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="riskyLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <div id="riskyDocumentsList" class="list-group list-group-flush">
                            <!-- Les documents à risque seront chargés ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="card chart-card">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-tools me-2"></i>
                            Actions Rapides
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('workflow_performance')">
                                    <i class="fas fa-download me-2"></i>Exporter Performance
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('bottlenecks')">
                                    <i class="fas fa-download me-2"></i>Exporter Goulots
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('risky_documents')">
                                    <i class="fas fa-download me-2"></i>Exporter Risques
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="{{ path('app_time_tracking') }}" class="btn btn-analytics w-100">
                                    <i class="fas fa-clock me-2"></i>Suivi Détaillé
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Variables globales pour les graphiques
let performanceChart = null;
let bottlenecksChart = null;
let trendsChart = null;

$(document).ready(function() {
    // Charger les données initiales
    loadAllCharts();

    // Gestionnaire pour les filtres
    $('#applyFilters').on('click', function() {
        loadAllCharts();
    });
});

function loadAllCharts() {
    loadWorkflowPerformance();
    loadBottlenecks();
    loadTrends();
    loadRiskyDocuments();
}

function loadWorkflowPerformance() {
    $('#performanceLoading').show();

    const params = new URLSearchParams({
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        doc_type: $('#docTypeFilter').val()
    });

    fetch(`{{ path('app_analytics_workflow_performance') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            $('#performanceLoading').hide();
            renderPerformanceChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des performances:', error);
            $('#performanceLoading').hide();
        });
}

function renderPerformanceChart(data) {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    if (performanceChart) {
        performanceChart.destroy();
    }

    if (!data || data.length === 0) {
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    // Créer un gradient de couleurs basé sur la durée
    const maxDuration = Math.max(...data.map(item => item.avg_duration_days));
    const colors = data.map(item => {
        const ratio = item.avg_duration_days / maxDuration;
        if (ratio > 0.8) return '#dc3545'; // Rouge pour les plus longs
        if (ratio > 0.6) return '#fd7e14'; // Orange
        if (ratio > 0.4) return '#ffc107'; // Jaune
        if (ratio > 0.2) return '#20c997'; // Vert clair
        return '#28a745'; // Vert pour les plus courts
    });

    performanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.state),
            datasets: [{
                label: 'Durée moyenne (jours)',
                data: data.map(item => item.avg_duration_days),
                backgroundColor: colors.map(color => color + '80'), // Ajouter transparence
                borderColor: colors,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Jours',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'États du workflow',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#009BFF',
                    borderWidth: 1,
                    callbacks: {
                        afterLabel: function(context) {
                            const item = data[context.dataIndex];
                            return [
                                `Documents: ${item.document_count}`,
                                `Min: ${item.min_duration_days} jours`,
                                `Max: ${item.max_duration_days} jours`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function loadBottlenecks() {
    $('#bottlenecksLoading').show();

    fetch(`{{ path('app_analytics_bottlenecks') }}?period=30`)
        .then(response => response.json())
        .then(data => {
            $('#bottlenecksLoading').hide();
            renderBottlenecksChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des goulots:', error);
            $('#bottlenecksLoading').hide();
        });
}

function renderBottlenecksChart(data) {
    const ctx = document.getElementById('bottlenecksChart').getContext('2d');

    if (bottlenecksChart) {
        bottlenecksChart.destroy();
    }

    if (!data || data.length === 0) {
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    const colors = data.map((item, index) => {
        if (item.severity_score > 6) return '#dc3545'; // Rouge
        if (item.severity_score > 4) return '#ffc107'; // Orange
        return '#28a745'; // Vert
    });

    bottlenecksChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.state),
            datasets: [{
                label: 'Score de sévérité',
                data: data.map(item => item.severity_score),
                backgroundColor: colors.map(color => color + '80'), // Ajouter transparence
                borderColor: colors,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Score de sévérité',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#009BFF',
                    borderWidth: 1,
                    callbacks: {
                        afterLabel: function(context) {
                            const item = data[context.dataIndex];
                            return [
                                `Durée moyenne: ${item.avg_duration_days} jours`,
                                `Documents: ${item.document_count}`,
                                `Rang: ${item.severity_rank}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function loadTrends() {
    $('#trendsLoading').show();

    fetch(`{{ path('app_analytics_workflow_trends') }}?period=6&granularity=month`)
        .then(response => response.json())
        .then(data => {
            $('#trendsLoading').hide();
            renderTrendsChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des tendances:', error);
            $('#trendsLoading').hide();
        });
}

function renderTrendsChart(data) {
    const ctx = document.getElementById('trendsChart').getContext('2d');

    if (trendsChart) {
        trendsChart.destroy();
    }

    if (!data || data.length === 0) {
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.period),
            datasets: [
                {
                    label: 'Nombre de documents',
                    data: data.map(item => item.document_count),
                    borderColor: '#009BFF',
                    backgroundColor: 'rgba(0, 155, 255, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#009BFF',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y'
                },
                {
                    label: 'Temps moyen (jours)',
                    data: data.map(item => item.avg_processing_time),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#dc3545',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Période',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Nombre de documents',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 155, 255, 0.1)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Jours',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            weight: 'bold'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#009BFF',
                    borderWidth: 1,
                }
            }
        }
    });
}

function loadRiskyDocuments() {
    $('#riskyLoading').show();

    fetch(`{{ path('app_analytics_risky_documents') }}?threshold=30`)
        .then(response => response.json())
        .then(data => {
            $('#riskyLoading').hide();
            renderRiskyDocuments(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des documents à risque:', error);
            $('#riskyLoading').hide();
        });
}

function renderRiskyDocuments(data) {
    const container = $('#riskyDocumentsList');
    container.empty();

    if (data.length === 0) {
        container.append('<div class="text-center text-muted p-3">Aucun document à risque</div>');
        return;
    }

    data.slice(0, 10).forEach(doc => {
        const riskClass = `risk-${doc.risk_level}`;
        const item = $(`
            <div class="list-group-item border-0 px-0">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${doc.reference} - ${doc.ref_rev}</h6>
                        <p class="mb-1 text-muted small">${doc.title || 'Sans titre'}</p>
                        <small class="text-muted">${doc.current_state} - ${doc.days_in_current_state} jours</small>
                    </div>
                    <span class="badge ${riskClass}">${doc.risk_level}</span>
                </div>
            </div>
        `);
        container.append(item);
    });
}

function exportAnalytics(type) {
    const params = new URLSearchParams({
        type: type,
        format: 'csv',
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val()
    });

    window.open(`{{ path('app_analytics_export') }}?${params}`, '_blank');
}
</script>
{% endblock %}
