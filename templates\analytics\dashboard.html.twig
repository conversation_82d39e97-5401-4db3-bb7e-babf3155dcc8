{% extends 'base.html.twig' %}

{% block title %}Analyse des Workflows{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .analytics-dashboard {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .kpi-card {
        background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
        color: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 155, 255, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .kpi-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 155, 255, 0.3);
    }

    .kpi-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .kpi-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .chart-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: none;
        transition: box-shadow 0.3s ease;
    }

    .chart-card:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .chart-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px 12px 0 0;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #495057;
        margin: 0;
    }

    .filter-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .btn-analytics {
        background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }

    .btn-analytics:hover {
        background: linear-gradient(135deg, #0056b3 0%, #003d82 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 155, 255, 0.3);
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
    }

    .risk-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 500;
    }

    .risk-low { background-color: #d4edda; color: #155724; }
    .risk-medium { background-color: #fff3cd; color: #856404; }
    .risk-high { background-color: #f8d7da; color: #721c24; }
    .risk-critical { background-color: #dc3545; color: white; }

    .metric-trend {
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }

    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
</style>
{% endblock %}

{% block body %}
<div class="analytics-dashboard">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h2 mb-3">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Analyse des Workflows et Performance
                </h1>
                <p class="text-muted">Tableau de bord d'analyse des durées, goulots d'étranglement et performance du système</p>
            </div>
        </div>

        <!-- KPIs -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.total_documents|number_format }}</div>
                        <div class="kpi-label">Total Documents</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.active_documents|number_format }}</div>
                        <div class="kpi-label">Documents Actifs</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.avg_days_since_be }}</div>
                        <div class="kpi-label">Jours Moy. depuis BE</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.risky_documents_count }}</div>
                        <div class="kpi-label">Documents à Risque</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.tracking_coverage }}%</div>
                        <div class="kpi-label">Couverture Suivi</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card kpi-card h-100">
                    <div class="card-body text-center">
                        <div class="kpi-value">{{ kpis.documents_with_tracking|number_format }}</div>
                        <div class="kpi-label">Avec Timestamps</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label for="startDate" class="form-label">Date de début</label>
                    <input type="date" id="startDate" class="form-control" value="{{ 'now'|date_modify('-30 days')|date('Y-m-d') }}">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">Date de fin</label>
                    <input type="date" id="endDate" class="form-control" value="{{ 'now'|date('Y-m-d') }}">
                </div>
                <div class="col-md-3">
                    <label for="docTypeFilter" class="form-label">Type de document</label>
                    <select id="docTypeFilter" class="form-select">
                        <option value="">Tous les types</option>
                        <option value="ASSY">ASSY</option>
                        <option value="PUR">PUR</option>
                        <option value="DOC">DOC</option>
                        <option value="MACH">MACH</option>
                        <option value="MOLD">MOLD</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button id="applyFilters" class="btn btn-analytics w-100">
                        <i class="fas fa-filter me-2"></i>Appliquer les filtres
                    </button>
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="row">
            <!-- Performance par état -->
            <div class="col-lg-6 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-stopwatch me-2"></i>
                            Performance par État
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="performanceLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <canvas id="performanceChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Goulots d'étranglement -->
            <div class="col-lg-6 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Goulots d'Étranglement
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="bottlenecksLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <canvas id="bottlenecksChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Tendances temporelles -->
            <div class="col-lg-8 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-line me-2"></i>
                            Tendances des Temps de Traitement
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="trendsLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <canvas id="trendsChart" width="600" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Documents à risque -->
            <div class="col-lg-4 mb-4">
                <div class="card chart-card h-100">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Documents à Risque
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="riskyLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <div id="riskyDocumentsList" class="list-group list-group-flush">
                            <!-- Les documents à risque seront chargés ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="card chart-card">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            <i class="fas fa-tools me-2"></i>
                            Actions Rapides
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('workflow_performance')">
                                    <i class="fas fa-download me-2"></i>Exporter Performance
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('bottlenecks')">
                                    <i class="fas fa-download me-2"></i>Exporter Goulots
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('risky_documents')">
                                    <i class="fas fa-download me-2"></i>Exporter Risques
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="{{ path('app_time_tracking') }}" class="btn btn-analytics w-100">
                                    <i class="fas fa-clock me-2"></i>Suivi Détaillé
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Variables globales pour les graphiques
let performanceChart = null;
let bottlenecksChart = null;
let trendsChart = null;

$(document).ready(function() {
    // Charger les données initiales
    loadAllCharts();

    // Gestionnaire pour les filtres
    $('#applyFilters').on('click', function() {
        loadAllCharts();
    });
});

function loadAllCharts() {
    loadWorkflowPerformance();
    loadBottlenecks();
    loadTrends();
    loadRiskyDocuments();
}

function loadWorkflowPerformance() {
    $('#performanceLoading').show();

    const params = new URLSearchParams({
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        doc_type: $('#docTypeFilter').val()
    });

    fetch(`{{ path('app_analytics_workflow_performance') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            $('#performanceLoading').hide();
            renderPerformanceChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des performances:', error);
            $('#performanceLoading').hide();
        });
}

function renderPerformanceChart(data) {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    if (performanceChart) {
        performanceChart.destroy();
    }

    performanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.state),
            datasets: [{
                label: 'Durée moyenne (jours)',
                data: data.map(item => item.avg_duration_days),
                backgroundColor: 'rgba(0, 155, 255, 0.7)',
                borderColor: 'rgba(0, 155, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Jours'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'États du workflow'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const item = data[context.dataIndex];
                            return [
                                `Documents: ${item.document_count}`,
                                `Min: ${item.min_duration_days} jours`,
                                `Max: ${item.max_duration_days} jours`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function loadBottlenecks() {
    $('#bottlenecksLoading').show();

    fetch(`{{ path('app_analytics_bottlenecks') }}?period=30`)
        .then(response => response.json())
        .then(data => {
            $('#bottlenecksLoading').hide();
            renderBottlenecksChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des goulots:', error);
            $('#bottlenecksLoading').hide();
        });
}

function renderBottlenecksChart(data) {
    const ctx = document.getElementById('bottlenecksChart').getContext('2d');

    if (bottlenecksChart) {
        bottlenecksChart.destroy();
    }

    const colors = data.map((item, index) => {
        if (item.severity_score > 6) return 'rgba(220, 53, 69, 0.8)'; // Rouge
        if (item.severity_score > 4) return 'rgba(255, 193, 7, 0.8)'; // Orange
        return 'rgba(40, 167, 69, 0.8)'; // Vert
    });

    bottlenecksChart = new Chart(ctx, {
        type: 'horizontalBar',
        data: {
            labels: data.map(item => item.state),
            datasets: [{
                label: 'Score de sévérité',
                data: data.map(item => item.severity_score),
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('0.8', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Score de sévérité'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const item = data[context.dataIndex];
                            return [
                                `Durée moyenne: ${item.avg_duration_days} jours`,
                                `Documents: ${item.document_count}`,
                                `Rang: ${item.severity_rank}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function loadTrends() {
    $('#trendsLoading').show();

    fetch(`{{ path('app_analytics_workflow_trends') }}?period=6&granularity=month`)
        .then(response => response.json())
        .then(data => {
            $('#trendsLoading').hide();
            renderTrendsChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des tendances:', error);
            $('#trendsLoading').hide();
        });
}

function renderTrendsChart(data) {
    const ctx = document.getElementById('trendsChart').getContext('2d');

    if (trendsChart) {
        trendsChart.destroy();
    }

    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.period),
            datasets: [
                {
                    label: 'Nombre de documents',
                    data: data.map(item => item.document_count),
                    borderColor: 'rgba(0, 155, 255, 1)',
                    backgroundColor: 'rgba(0, 155, 255, 0.1)',
                    yAxisID: 'y'
                },
                {
                    label: 'Temps moyen (jours)',
                    data: data.map(item => item.avg_processing_time),
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Période'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Nombre de documents'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Jours'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function loadRiskyDocuments() {
    $('#riskyLoading').show();

    fetch(`{{ path('app_analytics_risky_documents') }}?threshold=30`)
        .then(response => response.json())
        .then(data => {
            $('#riskyLoading').hide();
            renderRiskyDocuments(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des documents à risque:', error);
            $('#riskyLoading').hide();
        });
}

function renderRiskyDocuments(data) {
    const container = $('#riskyDocumentsList');
    container.empty();

    if (data.length === 0) {
        container.append('<div class="text-center text-muted p-3">Aucun document à risque</div>');
        return;
    }

    data.slice(0, 10).forEach(doc => {
        const riskClass = `risk-${doc.risk_level}`;
        const item = $(`
            <div class="list-group-item border-0 px-0">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${doc.reference} - ${doc.ref_rev}</h6>
                        <p class="mb-1 text-muted small">${doc.title || 'Sans titre'}</p>
                        <small class="text-muted">${doc.current_state} - ${doc.days_in_current_state} jours</small>
                    </div>
                    <span class="badge ${riskClass}">${doc.risk_level}</span>
                </div>
            </div>
        `);
        container.append(item);
    });
}

function exportAnalytics(type) {
    const params = new URLSearchParams({
        type: type,
        format: 'csv',
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val()
    });

    window.open(`{{ path('app_analytics_export') }}?${params}`, '_blank');
}
</script>
{% endblock %}
