{% extends 'base.html.twig' %}

{% block title %}Analyse des Workflows{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .analytics-dashboard {
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* KPI Cards avec couleurs vives */
    .kpi-card {
        border-radius: 24px;
        padding: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .kpi-card-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .kpi-card-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .kpi-card-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .kpi-card-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .kpi-card-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .kpi-card-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333 !important; }

    .kpi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.7s;
    }

    .kpi-card:hover::before {
        left: 100%;
    }

    .kpi-card:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    }

    .kpi-value {
        font-size: 3.5rem;
        font-weight: 900;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        line-height: 1;
    }

    .kpi-label {
        font-size: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1.5px;
        opacity: 0.9;
    }

    .kpi-icon {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        font-size: 2.5rem;
        opacity: 0.3;
    }

    /* Chart Cards */
    .chart-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 24px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    }

    .chart-header {
        background: linear-gradient(135deg, #009BFF 0%, #00d4ff 100%);
        color: white;
        padding: 2rem;
        position: relative;
        overflow: hidden;
    }

    .chart-header::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .chart-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .chart-subtitle {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    /* Filter Section */
    .filter-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 24px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filter-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Buttons */
    .btn-analytics {
        background: linear-gradient(135deg, #009BFF 0%, #00d4ff 100%);
        border: none;
        border-radius: 16px;
        color: white;
        font-weight: 700;
        padding: 1rem 2rem;
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(0, 155, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-analytics:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0, 155, 255, 0.4);
        color: white;
    }

    .btn-export {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3);
    }

    .btn-export:hover {
        box-shadow: 0 15px 35px rgba(67, 233, 123, 0.4);
    }

    /* Form Controls */
    .form-control, .form-select {
        border-radius: 16px;
        border: 2px solid rgba(0, 155, 255, 0.1);
        padding: 1rem 1.5rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #009BFF;
        box-shadow: 0 0 0 0.2rem rgba(0, 155, 255, 0.15);
        background: white;
    }

    .form-label {
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Risk Badges */
    .risk-badge {
        padding: 0.6rem 1.2rem;
        border-radius: 20px;
        font-weight: 700;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .risk-low {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }
    .risk-medium {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }
    .risk-high {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
        color: white;
    }
    .risk-critical {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0%, 100% { box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3); }
        50% { box-shadow: 0 8px 30px rgba(245, 87, 108, 0.6); }
    }

    /* Loading States */
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 4rem;
    }

    .spinner-border {
        width: 4rem;
        height: 4rem;
        border-width: 0.4rem;
        color: #009BFF;
    }

    /* Document List */
    .list-group-item {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 155, 255, 0.1);
        border-radius: 16px !important;
        margin-bottom: 0.75rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .list-group-item:hover {
        background: rgba(0, 155, 255, 0.05);
        transform: translateX(8px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Typography */
    h1, h2, h3, h4, h5 {
        color: #2c3e50;
        font-weight: 800;
    }

    .page-title {
        font-size: 3rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.2rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* Chart Container */
    .chart-container {
        position: relative;
        height: 400px;
        padding: 2rem;
    }

    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .kpi-value {
            font-size: 2.5rem;
        }

        .page-title {
            font-size: 2rem;
        }

        .chart-container {
            height: 300px;
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block body %}
<div class="analytics-dashboard">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="page-title fade-in-up">
                    <i class="fas fa-chart-line me-3"></i>
                    Analytics Dashboard
                </h1>
                <p class="page-subtitle fade-in-up">Analyse avancée des workflows et performance du système</p>
            </div>
        </div>

        <!-- KPIs -->
        <div class="row mb-5 g-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="kpi-card kpi-card-1 fade-in-up">
                    <i class="fas fa-file-alt kpi-icon"></i>
                    <div class="kpi-value">{{ kpis.total_documents|number_format }}</div>
                    <div class="kpi-label">Total Documents</div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="kpi-card kpi-card-2 fade-in-up" style="animation-delay: 0.1s">
                    <i class="fas fa-play-circle kpi-icon"></i>
                    <div class="kpi-value">{{ kpis.active_documents|number_format }}</div>
                    <div class="kpi-label">Documents Actifs</div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="kpi-card kpi-card-3 fade-in-up" style="animation-delay: 0.2s">
                    <i class="fas fa-clock kpi-icon"></i>
                    <div class="kpi-value">{{ kpis.avg_days_since_be }}</div>
                    <div class="kpi-label">Jours Moy. depuis BE</div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="kpi-card kpi-card-4 fade-in-up" style="animation-delay: 0.3s">
                    <i class="fas fa-exclamation-triangle kpi-icon"></i>
                    <div class="kpi-value">{{ kpis.risky_documents_count }}</div>
                    <div class="kpi-label">Documents à Risque</div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="kpi-card kpi-card-5 fade-in-up" style="animation-delay: 0.4s">
                    <i class="fas fa-percentage kpi-icon"></i>
                    <div class="kpi-value">{{ kpis.tracking_coverage }}%</div>
                    <div class="kpi-label">Couverture Suivi</div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="kpi-card kpi-card-6 fade-in-up" style="animation-delay: 0.5s">
                    <i class="fas fa-database kpi-icon"></i>
                    <div class="kpi-value">{{ kpis.documents_with_tracking|number_format }}</div>
                    <div class="kpi-label">Avec Timestamps</div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filter-section fade-in-up" style="animation-delay: 0.6s">
            <div class="filter-title">
                <i class="fas fa-sliders-h"></i>
                Filtres d'Analyse
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <label for="startDate" class="form-label">
                        <i class="fas fa-calendar-alt me-2"></i>Date de début
                    </label>
                    <input type="date" id="startDate" class="form-control" value="{{ 'now'|date_modify('-30 days')|date('Y-m-d') }}">
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="endDate" class="form-label">
                        <i class="fas fa-calendar-alt me-2"></i>Date de fin
                    </label>
                    <input type="date" id="endDate" class="form-control" value="{{ 'now'|date('Y-m-d') }}">
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="docTypeFilter" class="form-label">
                        <i class="fas fa-file-alt me-2"></i>Type de document
                    </label>
                    <select id="docTypeFilter" class="form-select">
                        <option value="">Tous les types</option>
                        <option value="ASSY">ASSY - Assemblage</option>
                        <option value="PUR">PUR - Achat</option>
                        <option value="DOC">DOC - Documentation</option>
                        <option value="MACH">MACH - Usinage</option>
                        <option value="MOLD">MOLD - Moulage</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-6 d-flex align-items-end">
                    <button id="applyFilters" class="btn btn-analytics w-100">
                        <i class="fas fa-search me-2"></i>Analyser
                    </button>
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="row g-4 mb-5">
            <!-- Performance par état -->
            <div class="col-xl-6 col-lg-12">
                <div class="chart-card fade-in-up" style="animation-delay: 0.7s">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Performance par État
                        </div>
                        <div class="chart-subtitle">Durées moyennes de traitement par étape du workflow</div>
                    </div>
                    <div class="chart-container">
                        <div class="loading-spinner" id="performanceLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <div class="mt-3">Analyse des performances...</div>
                        </div>
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Goulots d'étranglement -->
            <div class="col-xl-6 col-lg-12">
                <div class="chart-card fade-in-up" style="animation-delay: 0.8s">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Goulots d'Étranglement
                        </div>
                        <div class="chart-subtitle">États avec les plus longues durées de traitement</div>
                    </div>
                    <div class="chart-container">
                        <div class="loading-spinner" id="bottlenecksLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <div class="mt-3">Détection des blocages...</div>
                        </div>
                        <canvas id="bottlenecksChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4 mb-5">
            <!-- Tendances temporelles -->
            <div class="col-xl-8 col-lg-12">
                <div class="chart-card fade-in-up" style="animation-delay: 0.9s">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-line me-2"></i>
                            Tendances Temporelles
                        </div>
                        <div class="chart-subtitle">Évolution des volumes et temps de traitement sur 6 mois</div>
                    </div>
                    <div class="chart-container">
                        <div class="loading-spinner" id="trendsLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <div class="mt-3">Analyse des tendances...</div>
                        </div>
                        <canvas id="trendsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Documents à risque -->
            <div class="col-xl-4 col-lg-12">
                <div class="chart-card fade-in-up" style="animation-delay: 1s">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Documents à Risque
                        </div>
                        <div class="chart-subtitle">Documents bloqués depuis plus de 30 jours</div>
                    </div>
                    <div class="chart-container" style="height: auto; max-height: 400px; overflow-y: auto;">
                        <div class="loading-spinner" id="riskyLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <div class="mt-3">Recherche des risques...</div>
                        </div>
                        <div id="riskyDocumentsList" class="list-group list-group-flush">
                            <!-- Les documents à risque seront chargés ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="chart-card fade-in-up" style="animation-delay: 1.1s">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-rocket me-2"></i>
                            Actions Rapides
                        </div>
                        <div class="chart-subtitle">Exports et outils d'analyse avancée</div>
                    </div>
                    <div class="chart-container" style="height: auto;">
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-6">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('workflow_performance')">
                                    <i class="fas fa-chart-bar me-2"></i>Export Performance
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <button class="btn btn-export w-100" onclick="exportAnalytics('bottlenecks')">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Export Goulots
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <button class="btn btn-analytics w-100" onclick="exportAnalytics('risky_documents')">
                                    <i class="fas fa-exclamation-circle me-2"></i>Export Risques
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="{{ path('app_time_tracking') }}" class="btn btn-export w-100 text-decoration-none">
                                    <i class="fas fa-clock me-2"></i>Suivi Détaillé
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Variables globales pour les graphiques
let performanceChart = null;
let bottlenecksChart = null;
let trendsChart = null;

$(document).ready(function() {
    // Charger les données initiales
    loadAllCharts();

    // Gestionnaire pour les filtres
    $('#applyFilters').on('click', function() {
        loadAllCharts();
    });
});

function loadAllCharts() {
    loadWorkflowPerformance();
    loadBottlenecks();
    loadTrends();
    loadRiskyDocuments();
}

function loadWorkflowPerformance() {
    $('#performanceLoading').show();
    $('#performanceChart').hide();

    const params = new URLSearchParams({
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        doc_type: $('#docTypeFilter').val()
    });

    fetch(`{{ path('app_analytics_workflow_performance') }}?${params}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Performance data received:', data);
            $('#performanceLoading').hide();
            $('#performanceChart').show();
            renderPerformanceChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des performances:', error);
            $('#performanceLoading').hide();
            $('#performanceChart').show();

            // Afficher un message d'erreur sur le canvas
            const ctx = document.getElementById('performanceChart').getContext('2d');
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            ctx.fillStyle = '#dc3545';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Erreur de chargement des données', ctx.canvas.width / 2, ctx.canvas.height / 2);
            ctx.fillText('Vérifiez la console pour plus de détails', ctx.canvas.width / 2, ctx.canvas.height / 2 + 25);
        });
}

function renderPerformanceChart(data) {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    if (performanceChart) {
        performanceChart.destroy();
    }

    if (!data || data.length === 0) {
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    // Créer un gradient de couleurs basé sur la durée
    const maxDuration = Math.max(...data.map(item => item.avg_duration_days));
    const colors = data.map(item => {
        const ratio = item.avg_duration_days / maxDuration;
        if (ratio > 0.8) return '#dc3545'; // Rouge pour les plus longs
        if (ratio > 0.6) return '#fd7e14'; // Orange
        if (ratio > 0.4) return '#ffc107'; // Jaune
        if (ratio > 0.2) return '#20c997'; // Vert clair
        return '#28a745'; // Vert pour les plus courts
    });

    performanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.state),
            datasets: [{
                label: 'Durée moyenne (jours)',
                data: data.map(item => item.avg_duration_days),
                backgroundColor: colors.map(color => color + '80'), // Ajouter transparence
                borderColor: colors,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Jours',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'États du workflow',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#009BFF',
                    borderWidth: 1,
                    callbacks: {
                        afterLabel: function(context) {
                            const item = data[context.dataIndex];
                            return [
                                `Documents: ${item.document_count}`,
                                `Min: ${item.min_duration_days} jours`,
                                `Max: ${item.max_duration_days} jours`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function loadBottlenecks() {
    $('#bottlenecksLoading').show();
    $('#bottlenecksChart').hide();

    fetch(`{{ path('app_analytics_bottlenecks') }}?period=30`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Bottlenecks data received:', data);
            $('#bottlenecksLoading').hide();
            $('#bottlenecksChart').show();
            renderBottlenecksChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des goulots:', error);
            $('#bottlenecksLoading').hide();
            $('#bottlenecksChart').show();

            const ctx = document.getElementById('bottlenecksChart').getContext('2d');
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            ctx.fillStyle = '#dc3545';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Erreur de chargement des goulots', ctx.canvas.width / 2, ctx.canvas.height / 2);
        });
}

function renderBottlenecksChart(data) {
    const ctx = document.getElementById('bottlenecksChart').getContext('2d');

    if (bottlenecksChart) {
        bottlenecksChart.destroy();
    }

    if (!data || data.length === 0) {
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    const colors = data.map((item, index) => {
        if (item.severity_score > 6) return '#dc3545'; // Rouge
        if (item.severity_score > 4) return '#ffc107'; // Orange
        return '#28a745'; // Vert
    });

    bottlenecksChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.state),
            datasets: [{
                label: 'Score de sévérité',
                data: data.map(item => item.severity_score),
                backgroundColor: colors.map(color => color + '80'), // Ajouter transparence
                borderColor: colors,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Score de sévérité',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#009BFF',
                    borderWidth: 1,
                    callbacks: {
                        afterLabel: function(context) {
                            const item = data[context.dataIndex];
                            return [
                                `Durée moyenne: ${item.avg_duration_days} jours`,
                                `Documents: ${item.document_count}`,
                                `Rang: ${item.severity_rank}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function loadTrends() {
    $('#trendsLoading').show();

    fetch(`{{ path('app_analytics_workflow_trends') }}?period=6&granularity=month`)
        .then(response => response.json())
        .then(data => {
            $('#trendsLoading').hide();
            renderTrendsChart(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des tendances:', error);
            $('#trendsLoading').hide();
        });
}

function renderTrendsChart(data) {
    const ctx = document.getElementById('trendsChart').getContext('2d');

    if (trendsChart) {
        trendsChart.destroy();
    }

    if (!data || data.length === 0) {
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.period),
            datasets: [
                {
                    label: 'Nombre de documents',
                    data: data.map(item => item.document_count),
                    borderColor: '#009BFF',
                    backgroundColor: 'rgba(0, 155, 255, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#009BFF',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y'
                },
                {
                    label: 'Temps moyen (jours)',
                    data: data.map(item => item.avg_processing_time),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#dc3545',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Période',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Nombre de documents',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 155, 255, 0.1)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Jours',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            weight: 'bold'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#009BFF',
                    borderWidth: 1,
                }
            }
        }
    });
}

function loadRiskyDocuments() {
    $('#riskyLoading').show();

    fetch(`{{ path('app_analytics_risky_documents') }}?threshold=30`)
        .then(response => response.json())
        .then(data => {
            $('#riskyLoading').hide();
            renderRiskyDocuments(data);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des documents à risque:', error);
            $('#riskyLoading').hide();
        });
}

function renderRiskyDocuments(data) {
    const container = $('#riskyDocumentsList');
    container.empty();

    if (data.length === 0) {
        container.append('<div class="text-center text-muted p-3">Aucun document à risque</div>');
        return;
    }

    data.slice(0, 10).forEach(doc => {
        const riskClass = `risk-${doc.risk_level}`;
        const item = $(`
            <div class="list-group-item border-0 px-0">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${doc.reference} - ${doc.ref_rev}</h6>
                        <p class="mb-1 text-muted small">${doc.title || 'Sans titre'}</p>
                        <small class="text-muted">${doc.current_state} - ${doc.days_in_current_state} jours</small>
                    </div>
                    <span class="badge ${riskClass}">${doc.risk_level}</span>
                </div>
            </div>
        `);
        container.append(item);
    });
}

function exportAnalytics(type) {
    const params = new URLSearchParams({
        type: type,
        format: 'csv',
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val()
    });

    window.open(`{{ path('app_analytics_export') }}?${params}`, '_blank');
}
</script>
{% endblock %}
