<?php

namespace App\Controller;

use App\Entity\UserPreference;
use App\Repository\DocumentRepository;
use App\Repository\UserPreferenceRepository;
use App\Repository\UserRepository;
use App\Service\DashboardWidgetService;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/dashboard')]
class DashboardController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private UserPreferenceRepository $userPreferenceRepository;
    private DocumentRepository $documentRepository;
    private UserRepository $userRepository;
    private DashboardWidgetService $dashboardWidgetService;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserPreferenceRepository $userPreferenceRepository,
        DocumentRepository $documentRepository,
        UserRepository $userRepository,
        DashboardWidgetService $dashboardWidgetService
    ) {
        $this->entityManager = $entityManager;
        $this->userPreferenceRepository = $userPreferenceRepository;
        $this->documentRepository = $documentRepository;
        $this->userRepository = $userRepository;
        $this->dashboardWidgetService = $dashboardWidgetService;
    }

    #[Route('/', name: 'app_dashboard', methods: ['GET'])]
    public function index(): Response
    {
        $user = $this->getUser();

        // Récupérer les préférences de l'utilisateur
        $dashboardPreference = $this->userPreferenceRepository->findOneByKey($user, 'dashboard_widgets');

        // Widgets par défaut si aucune préférence n'est définie
        $defaultWidgets = [
            'recent_documents' => true,
            'document_stats' => true,
        ];

        $widgets = $dashboardPreference ? $dashboardPreference->getPreferenceValue() : $defaultWidgets;

        // Récupérer les données pour les widgets activés
        $dashboardData = [];

        if ($widgets['recent_documents'] ?? false) {
            $dashboardData['recent_documents'] = $this->documentRepository->findBy([], ['id' => 'DESC'], 10);
        }

        if ($widgets['document_stats'] ?? false) {
            $dashboardData['document_stats'] = [
                'total' => $this->documentRepository->countAllDocuments(),
                'by_type' => $this->documentRepository->getDocumentCountByType(),
            ];
        }



        // Nouveaux widgets
        if ($widgets['recent_activities'] ?? false) {
            $dashboardData['recent_activities'] = $this->dashboardWidgetService->getRecentActivities();
        }

        if ($widgets['pending_tasks'] ?? false) {
            $dashboardData['pending_tasks'] = $this->dashboardWidgetService->getPendingTasks();
        }





        return $this->render('dashboard/index.html.twig', [
            'widgets' => $widgets,
            'dashboard_data' => $dashboardData,
        ]);
    }

    #[Route('/customize', name: 'app_dashboard_customize', methods: ['GET'])]
    public function customize(): Response
    {
        $user = $this->getUser();

        // Récupérer les préférences de l'utilisateur
        $dashboardPreference = $this->userPreferenceRepository->findOneByKey($user, 'dashboard_widgets');

        // Widgets par défaut si aucune préférence n'est définie
        $defaultWidgets = [
            'recent_documents' => true,
            'document_stats' => true,
            'workflow_stats' => true,
            'risky_documents' => true,
            'processing_time_trends' => true,
            'document_distribution' => true,
        ];

        $widgets = $dashboardPreference ? $dashboardPreference->getPreferenceValue() : $defaultWidgets;

        // Liste de tous les widgets disponibles avec leurs descriptions
        $availableWidgets = [
            'recent_documents' => [
                'title' => 'Documents récents',
                'description' => 'Affiche les documents les plus récents',
                'icon' => 'fa-file-alt',
            ],
            'document_stats' => [
                'title' => 'Statistiques des documents',
                'description' => 'Affiche des statistiques générales sur les documents',
                'icon' => 'fa-chart-pie',
            ],
            'workflow_stats' => [
                'title' => 'Statistiques du workflow',
                'description' => 'Affiche des statistiques sur le workflow',
                'icon' => 'fa-project-diagram',
            ],
            'risky_documents' => [
                'title' => 'Documents à risque',
                'description' => 'Affiche les documents qui stagnent dans un état',
                'icon' => 'fa-exclamation-triangle',
            ],
            'processing_time_trends' => [
                'title' => 'Tendances des temps de traitement',
                'description' => 'Affiche l\'évolution des temps de traitement',
                'icon' => 'fa-chart-line',
            ],
            'document_distribution' => [
                'title' => 'Répartition des documents',
                'description' => 'Affiche la répartition des documents par département et utilisateur',
                'icon' => 'fa-users',
            ],
            'recent_activities' => [
                'title' => 'Activité récente',
                'description' => 'Affiche les dernières actions effectuées sur les documents',
                'icon' => 'fa-history',
            ],
            'pending_tasks' => [
                'title' => 'Mes tâches en attente',
                'description' => 'Affiche les documents qui attendent une action de votre part',
                'icon' => 'fa-tasks',
            ],
            'department_performance' => [
                'title' => 'Performance par département',
                'description' => 'Compare les performances des différents départements',
                'icon' => 'fa-building',
            ],
        ];

        return $this->render('dashboard/customize.html.twig', [
            'widgets' => $widgets,
            'available_widgets' => $availableWidgets,
        ]);
    }

    #[Route('/save-preferences', name: 'app_dashboard_save_preferences', methods: ['POST'])]
    public function savePreferences(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $data = json_decode($request->getContent(), true);

        if (!isset($data['widgets']) || !is_array($data['widgets'])) {
            return new JsonResponse(['success' => false, 'message' => 'Données invalides'], 400);
        }

        // Récupérer ou créer la préférence
        $preference = $this->userPreferenceRepository->findOneByKey($user, 'dashboard_widgets');

        if (!$preference) {
            $preference = new UserPreference();
            $preference->setUser($user);
            $preference->setPreferenceKey('dashboard_widgets');
        }

        $preference->setPreferenceValue($data['widgets']);
        $preference->updateTimestamp();

        $this->entityManager->persist($preference);
        $this->entityManager->flush();

        return new JsonResponse(['success' => true]);
    }














}
