<?php

/**
 * Script de test pour valider les améliorations de la migration des state_timestamps
 * 
 * Ce script teste la nouvelle logique de création des state_timestamps avec :
 * - Gestion complète des transitions (enter/exit)
 * - Calcul correct des from_state
 * - Respect de l'ordre chronologique du workflow
 */

require_once __DIR__ . '/vendor/autoload.php';

use Symfony\Component\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;

class StateTimestampsTestSuite
{
    private array $testCases = [];
    
    public function __construct()
    {
        $this->setupTestCases();
    }
    
    /**
     * Définir les cas de test avec différents scénarios de workflow
     */
    private function setupTestCases(): void
    {
        // Cas 1: Document simple avec workflow BE complet
        $this->testCases['simple_be_workflow'] = [
            'description' => 'Document avec workflow BE complet (BE_0 → BE_1 → BE → Produit)',
            'legacy_data' => [
                'Rel_Pack_Num' => 'TEST001',
                'Doc_Type' => 'ASSY',
                'Reference' => 'TEST-DOC-001'
            ],
            'package_data' => [
                'Creation_VISA' => 'ADMIN',
                'Creation_Date' => '2024-01-01 10:00:00',
                'VISA_BE_2' => 'USER1',
                'DATE_BE_2' => '2024-01-02 14:00:00',
                'VISA_BE_3' => 'USER2', 
                'DATE_BE_3' => '2024-01-03 16:00:00'
            ],
            'document_visas' => [
                'VISA_Product' => 'USER3',
                'DATE_Product' => '2024-01-04 09:00:00'
            ],
            'expected_states' => ['BE_0', 'BE_1', 'BE', 'Produit'],
            'expected_transitions' => [
                'BE_0' => ['enter' => '2024-01-01 10:00:00', 'exit' => '2024-01-02 14:00:00', 'from_state' => null],
                'BE_1' => ['enter' => '2024-01-02 14:00:00', 'exit' => '2024-01-03 16:00:00', 'from_state' => 'BE_0'],
                'BE' => ['enter' => '2024-01-03 16:00:00', 'exit' => '2024-01-04 09:00:00', 'from_state' => 'BE_1'],
                'Produit' => ['enter' => '2024-01-04 09:00:00', 'exit' => null, 'from_state' => 'BE']
            ]
        ];

        // Cas 2: Document PUR avec workflow d'achat
        $this->testCases['pur_workflow'] = [
            'description' => 'Document PUR avec workflow d\'achat complet',
            'legacy_data' => [
                'Rel_Pack_Num' => 'TEST002',
                'Doc_Type' => 'PUR',
                'Proc_Type' => 'F',
                'Reference' => 'TEST-PUR-001'
            ],
            'package_data' => [
                'Creation_VISA' => 'ADMIN',
                'Creation_Date' => '2024-01-01 10:00:00',
                'VISA_BE_2' => 'USER1',
                'DATE_BE_2' => '2024-01-02 14:00:00',
                'VISA_BE_3' => 'USER2',
                'DATE_BE_3' => '2024-01-03 16:00:00'
            ],
            'document_visas' => [
                'VISA_Product' => 'USER3',
                'DATE_Product' => '2024-01-04 09:00:00',
                'VISA_Quality' => 'USER4',
                'DATE_Quality' => '2024-01-05 11:00:00',
                'VISA_PUR_1' => 'USER5',
                'DATE_PUR_1' => '2024-01-06 13:00:00',
                'VISA_Metro' => 'USER6',
                'DATE_Metro' => '2024-01-07 15:00:00'
            ],
            'expected_states' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Quality', 'Achat_Rfq', 'Metro'],
            'expected_transitions' => [
                'BE_0' => ['enter' => '2024-01-01 10:00:00', 'exit' => '2024-01-02 14:00:00', 'from_state' => null],
                'BE_1' => ['enter' => '2024-01-02 14:00:00', 'exit' => '2024-01-03 16:00:00', 'from_state' => 'BE_0'],
                'BE' => ['enter' => '2024-01-03 16:00:00', 'exit' => '2024-01-04 09:00:00', 'from_state' => 'BE_1'],
                'Produit' => ['enter' => '2024-01-04 09:00:00', 'exit' => '2024-01-05 11:00:00', 'from_state' => 'BE'],
                'Quality' => ['enter' => '2024-01-05 11:00:00', 'exit' => '2024-01-06 13:00:00', 'from_state' => 'Produit'],
                'Achat_Rfq' => ['enter' => '2024-01-06 13:00:00', 'exit' => '2024-01-07 15:00:00', 'from_state' => 'Quality'],
                'Metro' => ['enter' => '2024-01-07 15:00:00', 'exit' => null, 'from_state' => 'Achat_Rfq']
            ]
        ];

        // Cas 3: Document avec états parallèles
        $this->testCases['parallel_states'] = [
            'description' => 'Document avec états parallèles (Assembly + Planning)',
            'legacy_data' => [
                'Rel_Pack_Num' => 'TEST003',
                'Doc_Type' => 'ASSY',
                'Proc_Type' => 'E',
                'Reference' => 'TEST-ASSY-001'
            ],
            'package_data' => [
                'Creation_VISA' => 'ADMIN',
                'Creation_Date' => '2024-01-01 10:00:00',
                'VISA_BE_3' => 'USER2',
                'DATE_BE_3' => '2024-01-03 16:00:00'
            ],
            'document_visas' => [
                'VISA_Product' => 'USER3',
                'DATE_Product' => '2024-01-04 09:00:00',
                'VISA_Prod' => 'USER4',
                'DATE_Prod' => '2024-01-05 11:00:00',
                'VISA_Supply' => 'USER5',
                'DATE_Supply' => '2024-01-06 13:00:00'
            ],
            'expected_states' => ['BE_0', 'BE', 'Produit', 'Assembly', 'Planning'],
            'expected_transitions' => [
                'BE_0' => ['enter' => '2024-01-01 10:00:00', 'exit' => '2024-01-03 16:00:00', 'from_state' => null],
                'BE' => ['enter' => '2024-01-03 16:00:00', 'exit' => '2024-01-04 09:00:00', 'from_state' => 'BE_0'],
                'Produit' => ['enter' => '2024-01-04 09:00:00', 'exit' => '2024-01-05 11:00:00', 'from_state' => 'BE'],
                'Assembly' => ['enter' => '2024-01-05 11:00:00', 'exit' => '2024-01-06 13:00:00', 'from_state' => 'Produit'],
                'Planning' => ['enter' => '2024-01-06 13:00:00', 'exit' => null, 'from_state' => 'Assembly']
            ]
        ];
    }

    /**
     * Exécuter tous les tests
     */
    public function runAllTests(): void
    {
        echo "=== Test Suite: State Timestamps Migration ===\n\n";
        
        $totalTests = count($this->testCases);
        $passedTests = 0;
        
        foreach ($this->testCases as $testName => $testCase) {
            echo "Test: {$testCase['description']}\n";
            echo str_repeat("-", 50) . "\n";
            
            if ($this->runSingleTest($testName, $testCase)) {
                echo "✅ PASSED\n\n";
                $passedTests++;
            } else {
                echo "❌ FAILED\n\n";
            }
        }
        
        echo "=== Résultats ===\n";
        echo "Tests passés: {$passedTests}/{$totalTests}\n";
        echo "Taux de réussite: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";
    }

    /**
     * Exécuter un test individuel
     */
    private function runSingleTest(string $testName, array $testCase): bool
    {
        try {
            // Simuler la création des state_timestamps avec la nouvelle logique
            $stateTimestamps = $this->simulateStateTimestampsCreation($testCase);
            
            // Valider les résultats
            return $this->validateResults($stateTimestamps, $testCase);
            
        } catch (Exception $e) {
            echo "Erreur lors du test: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Simuler la création des state_timestamps (version simplifiée pour test)
     */
    private function simulateStateTimestampsCreation(array $testCase): array
    {
        // Cette fonction simule la logique améliorée de createStateTimestamps
        // En production, cela serait fait par la vraie méthode dans MigrateLegacyDataOptimizedCommand
        
        $stateTimestamps = [];
        $allEvents = [];
        
        // Collecter les événements BE
        $packageData = $testCase['package_data'];
        if (!empty($packageData['Creation_VISA'])) {
            $allEvents[] = [
                'date' => $packageData['Creation_Date'],
                'state' => 'BE_0',
                'type' => 'enter',
                'from_state' => null
            ];
        }
        
        if (!empty($packageData['VISA_BE_2'])) {
            $allEvents[] = [
                'date' => $packageData['DATE_BE_2'],
                'state' => 'BE_0',
                'type' => 'exit'
            ];
            $allEvents[] = [
                'date' => $packageData['DATE_BE_2'],
                'state' => 'BE_1',
                'type' => 'enter',
                'from_state' => 'BE_0'
            ];
        }
        
        if (!empty($packageData['VISA_BE_3'])) {
            $allEvents[] = [
                'date' => $packageData['DATE_BE_3'],
                'state' => 'BE_1',
                'type' => 'exit'
            ];
            $allEvents[] = [
                'date' => $packageData['DATE_BE_3'],
                'state' => 'BE',
                'type' => 'enter',
                'from_state' => 'BE_1'
            ];
        }
        
        // Collecter les événements des visas documents
        $documentVisas = $testCase['document_visas'] ?? [];
        $visaStateMapping = [
            'VISA_Product' => 'Produit',
            'VISA_Quality' => 'Quality',
            'VISA_PUR_1' => 'Achat_Rfq',
            'VISA_Metro' => 'Metro',
            'VISA_Prod' => $this->getProductionState($testCase['legacy_data']['Doc_Type']),
            'VISA_Supply' => 'Planning'
        ];
        
        foreach ($visaStateMapping as $visaField => $stateName) {
            if (!empty($documentVisas[$visaField])) {
                $dateField = str_replace('VISA_', 'DATE_', $visaField);
                $allEvents[] = [
                    'date' => $documentVisas[$dateField],
                    'state' => $stateName,
                    'type' => 'visa_completion'
                ];
            }
        }
        
        // Trier par date
        usort($allEvents, function($a, $b) {
            return strcmp($a['date'], $b['date']);
        });
        
        // Construire les timestamps
        $activeStates = [];
        foreach ($allEvents as $event) {
            $state = $event['state'];
            $date = $event['date'];
            $type = $event['type'];
            
            if (!isset($stateTimestamps[$state])) {
                $stateTimestamps[$state] = [];
            }
            
            if ($type === 'enter') {
                $stateTimestamps[$state][] = [
                    'enter' => $date,
                    'exit' => null,
                    'from_state' => $event['from_state'] ?? null
                ];
                $activeStates[$state] = count($stateTimestamps[$state]) - 1;
                
            } elseif ($type === 'exit' && isset($activeStates[$state])) {
                $activeIndex = $activeStates[$state];
                $stateTimestamps[$state][$activeIndex]['exit'] = $date;
                unset($activeStates[$state]);
                
            } elseif ($type === 'visa_completion') {
                // Simuler la logique de transition basée sur le visa
                $this->processVisaTransition($state, $date, $stateTimestamps, $activeStates, $testCase);
            }
        }
        
        return $stateTimestamps;
    }

    private function getProductionState(string $docType): string
    {
        switch ($docType) {
            case 'ASSY': return 'Assembly';
            case 'MACH': return 'Machining';
            case 'MOLD': return 'Molding';
            default: return 'Assembly';
        }
    }

    private function processVisaTransition(string $state, string $date, array &$stateTimestamps, array &$activeStates, array $testCase): void
    {
        // Logique simplifiée pour les tests
        // En réalité, cette logique serait plus complexe et basée sur les conditions du workflow
        
        // Fermer l'état précédent s'il est actif
        $workflowOrder = ['BE_0', 'BE_1', 'BE', 'Produit', 'Quality', 'Achat_Rfq', 'Metro', 'Assembly', 'Planning'];
        $currentIndex = array_search($state, $workflowOrder);
        
        if ($currentIndex > 0) {
            $previousState = $workflowOrder[$currentIndex - 1];
            if (isset($activeStates[$previousState])) {
                $activeIndex = $activeStates[$previousState];
                $stateTimestamps[$previousState][$activeIndex]['exit'] = $date;
                unset($activeStates[$previousState]);
            }
        }
        
        // Entrer dans le nouvel état
        if (!isset($stateTimestamps[$state])) {
            $stateTimestamps[$state] = [];
        }
        
        $fromState = $currentIndex > 0 ? $workflowOrder[$currentIndex - 1] : null;
        $stateTimestamps[$state][] = [
            'enter' => $date,
            'exit' => null,
            'from_state' => $fromState
        ];
        $activeStates[$state] = count($stateTimestamps[$state]) - 1;
    }

    /**
     * Valider les résultats du test
     */
    private function validateResults(array $actualTimestamps, array $testCase): bool
    {
        $expectedTransitions = $testCase['expected_transitions'];
        $isValid = true;
        
        foreach ($expectedTransitions as $state => $expectedTransition) {
            if (!isset($actualTimestamps[$state])) {
                echo "❌ État manquant: {$state}\n";
                $isValid = false;
                continue;
            }
            
            $actualTransition = $actualTimestamps[$state][0]; // Prendre la première entrée
            
            // Vérifier la date d'entrée
            if ($actualTransition['enter'] !== $expectedTransition['enter']) {
                echo "❌ Date d'entrée incorrecte pour {$state}: attendu {$expectedTransition['enter']}, obtenu {$actualTransition['enter']}\n";
                $isValid = false;
            }
            
            // Vérifier la date de sortie
            if ($actualTransition['exit'] !== $expectedTransition['exit']) {
                echo "❌ Date de sortie incorrecte pour {$state}: attendu " . ($expectedTransition['exit'] ?? 'null') . ", obtenu " . ($actualTransition['exit'] ?? 'null') . "\n";
                $isValid = false;
            }
            
            // Vérifier le from_state
            if ($actualTransition['from_state'] !== $expectedTransition['from_state']) {
                echo "❌ From_state incorrect pour {$state}: attendu " . ($expectedTransition['from_state'] ?? 'null') . ", obtenu " . ($actualTransition['from_state'] ?? 'null') . "\n";
                $isValid = false;
            }
            
            if ($isValid) {
                echo "✅ État {$state}: transitions correctes\n";
            }
        }
        
        return $isValid;
    }
}

// Exécuter les tests
$testSuite = new StateTimestampsTestSuite();
$testSuite->runAllTests();
