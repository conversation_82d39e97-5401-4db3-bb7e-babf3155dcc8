<?php

namespace App\Repository;

use App\Entity\Document;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Document>
 */
class DocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Document::class);
    }

    /**
     * Trouve les documents par étape de workflow actuelle
     */
    public function findByCurrentStepNative(string $position)
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, ['$."' . $position . '"']);
        $documentsData = $result->fetchAllAssociative();

        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve le dernier document par référence
     */
    public function findLatestByReference(string $reference): ?Document
    {
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
            ->andWhere('d.reference = :reference')
            ->setParameter('reference', $reference)
            ->orderBy('d.refRev', 'DESC')
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * Trouve des documents similaires
     */
    public function findSimilarDocuments(?string $docType, ?string $procType, ?string $materialType, int $limit = 20): array
    {
        $qb = $this->createQueryBuilder('d');

        if ($docType) {
            $qb->andWhere('d.docType = :docType')
               ->setParameter('docType', $docType);
        }

        if ($procType) {
            $qb->andWhere('d.procType = :procType')
               ->setParameter('procType', $procType);
        }

        if ($materialType) {
            $qb->andWhere('d.Material_Type = :materialType')
               ->setParameter('materialType', $materialType);
        }

        return $qb->setMaxResults($limit)
                 ->getQuery()
                 ->getResult();
    }

    /**
     * Trouve les documents à valider par un utilisateur
     */
    public function findDocumentsToValidateByUser($user): array
    {
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->innerJoin('d.visas', 'v')
           ->innerJoin('v.validator', 'u')
           ->where('u.id = :userId')
           ->andWhere('v.dateVisa IS NULL')
           ->setParameter('userId', $user->getId())
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Compte les documents par étape de workflow
     */
    public function countDocumentsByWorkflowStep(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $workflowSteps = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $counts = [];

        foreach ($workflowSteps as $step) {
            if ($step === 'Qual_Logistique' || $step === 'Logistique') {
                if (!isset($counts['Qual_Logistique'])) {
                    $sql = "
                        SELECT COUNT(DISTINCT d.id) as count_docs
                        FROM document d
                        WHERE (
                            JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                            OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
                        )
                        AND NOT (
                            EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                            AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
                        )
                    ";

                    $result = $conn->executeQuery($sql);
                    $count = $result->fetchOne();

                    if ($count > 0) {
                        $counts['Qual_Logistique'] = (int)$count;
                        $counts['Logistique'] = (int)$count;
                    }
                }
            } else {
                $sql = "
                    SELECT COUNT(d.id) as count_docs
                    FROM document d
                    WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                    AND NOT EXISTS (
                        SELECT 1 FROM visa v
                        WHERE v.released_drawing_id = d.id
                        AND v.name = ?
                        AND v.status = 'valid'
                    )
                ";

                $result = $conn->executeQuery($sql, [
                    '$."' . $step . '"',
                    'visa_' . $step
                ]);
                $count = $result->fetchOne();

                if ($count > 0) {
                    $counts[$step] = (int)$count;
                }
            }
        }

        return $counts;
    }

    /**
     * Charge les documents avec leurs relations
     */
    public function findDocumentsWithRelations(array $documentIds): array
    {
        if (empty($documentIds)) {
            return [];
        }

        $qb = $this->createQueryBuilder('d');
        $qb->select('d', 'v', 'validator', 'rp', 'superviseur', 'c')
           ->leftJoin('d.visas', 'v')
           ->leftJoin('v.validator', 'validator')
           ->leftJoin('d.relPack', 'rp')
           ->leftJoin('d.superviseur', 'superviseur')
           ->leftJoin('d.commentaires', 'c')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Recherche de documents par critères multiples
     */
    public function searchDocuments(array $criteria, int $limit = 50): array
    {
        $qb = $this->createQueryBuilder('d');

        if (!empty($criteria['reference'])) {
            $qb->andWhere('d.reference LIKE :reference')
               ->setParameter('reference', '%' . $criteria['reference'] . '%');
        }

        if (!empty($criteria['docType'])) {
            $qb->andWhere('d.docType = :docType')
               ->setParameter('docType', $criteria['docType']);
        }

        if (!empty($criteria['material'])) {
            $qb->andWhere('d.material LIKE :material')
               ->setParameter('material', '%' . $criteria['material'] . '%');
        }

        if (!empty($criteria['supervisor'])) {
            $qb->andWhere('d.superviseur = :supervisor')
               ->setParameter('supervisor', $criteria['supervisor']);
        }

        return $qb->orderBy('d.id', 'DESC')
                 ->setMaxResults($limit)
                 ->getQuery()
                 ->getResult();
    }

    /**
     * Compte le nombre total de documents
     */
    public function countAllDocuments(): int
    {
        return $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Récupère les documents récents
     */
    public function findRecentDocuments(int $limit = 10): array
    {
        return $this->createQueryBuilder('d')
            ->orderBy('d.id', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les documents par type
     */
    public function findByDocType(string $docType): array
    {
        return $this->createQueryBuilder('d')
            ->andWhere('d.docType = :docType')
            ->setParameter('docType', $docType)
            ->orderBy('d.id', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les documents par superviseur
     */
    public function findBySupervisor($supervisor): array
    {
        return $this->createQueryBuilder('d')
            ->andWhere('d.superviseur = :supervisor')
            ->setParameter('supervisor', $supervisor)
            ->orderBy('d.id', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les documents actifs dans une étape spécifique
     */
    public function findActiveDocumentsInStep(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.id
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = ?
                AND v.status = 'valid'
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            '$."' . $step . '"',
            'visa_' . $step
        ]);
        $documentIds = $result->fetchFirstColumn();

        if (empty($documentIds)) {
            return [];
        }

        return $this->findDocumentsWithRelations($documentIds);
    }

    /**
     * Version avec cache du comptage des documents par étape
     */
    public function countDocumentsByWorkflowStepCached(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 2 minutes
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 120) {
            $cache = $this->countDocumentsByWorkflowStep();
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Récupère le nombre de documents par type
     */
    public function getDocumentCountByType(): array
    {
        $result = $this->createQueryBuilder('d')
            ->select('d.docType, COUNT(d.id) as count')
            ->groupBy('d.docType')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach ($result as $row) {
            $counts[$row['docType'] ?? 'Unknown'] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Trouve les documents par période
     */
    public function findByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND (
                EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        d.state_timestamps,
                        '$.*[*]' COLUMNS (
                            enter_date VARCHAR(255) PATH '$.enter'
                        )
                    ) jt2
                    WHERE STR_TO_DATE(jt2.enter_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
                )
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        $documentsData = $result->fetchAllAssociative();

        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents à réviser par un utilisateur
     */
    public function findDocumentsToReviewByUser($user): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.superviseur_id = ?
            AND d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND (
                JSON_SEARCH(d.state_timestamps, 'one', '%reject%', NULL, '$.*[*].from_state') IS NOT NULL
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [$user->getId()]);
        $documentsData = $result->fetchAllAssociative();

        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents traités entre deux dates
     */
    public function findDocumentsProcessedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND EXISTS (
                SELECT 1 FROM JSON_TABLE(
                    d.state_timestamps,
                    '$.*[*]' COLUMNS (
                        exit_date VARCHAR(255) PATH '$.exit'
                    )
                ) jt
                WHERE jt.exit_date IS NOT NULL
                AND jt.exit_date != ''
                AND STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        $documentsData = $result->fetchAllAssociative();

        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents actifs dans les étapes logistiques (Qual_Logistique et Logistique)
     */
    public function findActiveDocumentsInLogisticsSteps(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.id
            FROM document d
            WHERE (
                JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
            )
            AND NOT (
                EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql);
        $documentIds = $result->fetchFirstColumn();

        if (empty($documentIds)) {
            return [];
        }

        return $this->findDocumentsWithRelations($documentIds);
    }

    /**
     * Récupère les statistiques optimisées pour la navbar
     */
    public function getNavbarStatsOptimized(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 5 minutes
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 300) {
            $conn = $this->getEntityManager()->getConnection();

            $sql = "
                SELECT
                    COUNT(DISTINCT d.id) as total_documents,
                    COUNT(DISTINCT CASE
                        WHEN d.state_timestamps IS NOT NULL
                        AND d.state_timestamps != '{}'
                        AND d.state_timestamps != ''
                        AND NOT JSON_CONTAINS_PATH(d.current_steps, 'one', '$.BE_0', '$.BE_1', '$.BE')
                        THEN d.id
                    END) as documents_out_of_be,
                    AVG(CASE
                        WHEN d.state_timestamps IS NOT NULL
                        AND d.state_timestamps != '{}'
                        AND d.state_timestamps != ''
                        AND JSON_EXTRACT(d.state_timestamps, '$.BE') IS NOT NULL
                        THEN DATEDIFF(NOW(), STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE[0].enter')), '%Y-%m-%d %H:%i:%s'))
                    END) as avg_days_since_be,
                    MAX(CASE
                        WHEN d.state_timestamps IS NOT NULL
                        AND d.state_timestamps != '{}'
                        AND d.state_timestamps != ''
                        AND JSON_EXTRACT(d.state_timestamps, '$.BE') IS NOT NULL
                        THEN DATEDIFF(NOW(), STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE[0].enter')), '%Y-%m-%d %H:%i:%s'))
                    END) as max_days_since_be
                FROM document d
            ";

            $result = $conn->executeQuery($sql)->fetchAssociative();

            // Trouver le document avec le maximum de jours
            $documentWithMaxDays = null;
            if ($result['max_days_since_be']) {
                $sqlMaxDoc = "
                    SELECT d.reference, d.ref_rev
                    FROM document d
                    WHERE d.state_timestamps IS NOT NULL
                    AND d.state_timestamps != '{}'
                    AND d.state_timestamps != ''
                    AND JSON_EXTRACT(d.state_timestamps, '$.BE') IS NOT NULL
                    AND DATEDIFF(NOW(), STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE[0].enter')), '%Y-%m-%d %H:%i:%s')) = ?
                    LIMIT 1
                ";

                $maxDocResult = $conn->executeQuery($sqlMaxDoc, [$result['max_days_since_be']])->fetchAssociative();
                if ($maxDocResult) {
                    $documentWithMaxDays = $maxDocResult['reference'] . ' - ' . $maxDocResult['ref_rev'];
                }
            }

            $cache = [
                'totalDocuments' => (int)$result['total_documents'],
                'documentsOutOfBE' => (int)$result['documents_out_of_be'],
                'avgDaysSinceBE' => round((float)$result['avg_days_since_be'], 1),
                'maxDaysSinceBE' => (int)$result['max_days_since_be'],
                'documentWithMaxDays' => $documentWithMaxDays
            ];
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Analyse les durées par état avec optimisation
     */
    public function getStateDurationAnalysis(string $state, int $limitDays = 90): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                d.id,
                d.reference,
                d.ref_rev,
                d.doc_type,
                d.proc_type,
                jt.enter_date,
                jt.exit_date,
                CASE
                    WHEN jt.exit_date IS NOT NULL
                    THEN DATEDIFF(
                        STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                    ELSE DATEDIFF(
                        NOW(),
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    )
                END as duration_days,
                CASE WHEN jt.exit_date IS NULL THEN 1 ELSE 0 END as is_active
            FROM document d
            CROSS JOIN JSON_TABLE(
                d.state_timestamps,
                '$.\"{$state}\"[*]' COLUMNS (
                    enter_date VARCHAR(255) PATH '$.enter',
                    exit_date VARCHAR(255) PATH '$.exit'
                )
            ) jt
            WHERE jt.enter_date IS NOT NULL
            AND STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ORDER BY duration_days DESC
            LIMIT 1000
        ";

        $results = $conn->executeQuery($sql, [$limitDays])->fetchAllAssociative();

        return array_map(function($row) {
            return [
                'document_id' => (int)$row['id'],
                'reference' => $row['reference'],
                'ref_rev' => $row['ref_rev'],
                'doc_type' => $row['doc_type'],
                'proc_type' => $row['proc_type'],
                'enter_date' => $row['enter_date'],
                'exit_date' => $row['exit_date'],
                'duration_days' => (int)$row['duration_days'],
                'is_active' => (bool)$row['is_active']
            ];
        }, $results);
    }

    /**
     * Récupère les documents avec les durées les plus longues par état
     */
    public function getLongestDurationsByState(int $limit = 10): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $workflowSteps = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $results = [];

        foreach ($workflowSteps as $state) {
            $sql = "
                SELECT
                    d.id,
                    d.reference,
                    d.ref_rev,
                    '{$state}' as state,
                    DATEDIFF(
                        COALESCE(STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'), NOW()),
                        STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                    ) as duration_days,
                    CASE WHEN jt.exit_date IS NULL THEN 1 ELSE 0 END as is_active
                FROM document d
                CROSS JOIN JSON_TABLE(
                    d.state_timestamps,
                    '$.\"{$state}\"[*]' COLUMNS (
                        enter_date VARCHAR(255) PATH '$.enter',
                        exit_date VARCHAR(255) PATH '$.exit'
                    )
                ) jt
                WHERE jt.enter_date IS NOT NULL
                ORDER BY duration_days DESC
                LIMIT ?
            ";

            $stateResults = $conn->executeQuery($sql, [$limit])->fetchAllAssociative();

            foreach ($stateResults as $row) {
                $results[] = [
                    'document_id' => (int)$row['id'],
                    'reference' => $row['reference'],
                    'ref_rev' => $row['ref_rev'],
                    'state' => $row['state'],
                    'duration_days' => (int)$row['duration_days'],
                    'is_active' => (bool)$row['is_active']
                ];
            }
        }

        // Trier par durée décroissante
        usort($results, fn($a, $b) => $b['duration_days'] <=> $a['duration_days']);

        return array_slice($results, 0, $limit * 3); // Retourner plus de résultats pour avoir une vue d'ensemble
    }
}
